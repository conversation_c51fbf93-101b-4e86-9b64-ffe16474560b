"""
数据库模型定义与交互 (使用 SQLAlchemy ORM)
"""

from contextlib import contextmanager
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, Iterator, List, Optional, Tuple
from typing import cast as typing_cast

import numpy as np
from sqlalchemy import (
    Boolean,
    JSON,
    DateTime,
    Float,
    ForeignKey,
    Integer,
    LargeBinary,
    String,
    Text,
    UniqueConstraint,
    cast,
    create_engine,
)
from sqlalchemy.orm import Mapped, Session, declarative_base, mapped_column, relationship, sessionmaker
from sqlalchemy.sql import func
from sqlalchemy.sql.functions import coalesce

from config.settings import settings
from utils.logger import get_logger

# 1. SQLAlchemy 引擎配置
DATABASE_URL = (
    f"postgresql+psycopg2://{settings.POSTGRES_USER}:{settings.POSTGRES_PASSWORD}@"
    f"{settings.POSTGRES_HOST}:{settings.POSTGRES_PORT}/{settings.POSTGRES_DB}"
)
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 2. 创建声明式基类
Base = declarative_base()


# 3. 定义所有数据模型类
class VideoMeta(Base):
    __tablename__ = "video_meta"
    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    video_name: Mapped[str] = mapped_column(String, nullable=False)
    total_duration: Mapped[float] = mapped_column(Float, nullable=False)
    fps: Mapped[float] = mapped_column(Float, nullable=False)
    input_file_path: Mapped[Optional[str]] = mapped_column(String, unique=True)
    file_hash: Mapped[Optional[str]] = mapped_column(String, unique=True)
    global_summary: Mapped[Optional[str]] = mapped_column(Text)
    key_themes: Mapped[Optional[str]] = mapped_column(Text)
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now())
    updated_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), onupdate=func.now())
    status: Mapped[str] = mapped_column(String, default="pending")


class StorySequence(Base):
    """存储序列（由多个场景组成）的分析结果"""

    __tablename__ = "story_sequences"
    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    video_id: Mapped[int] = mapped_column(Integer, ForeignKey("video_meta.id", ondelete="CASCADE"), nullable=False)
    sequence_number: Mapped[int] = mapped_column(Integer, nullable=False)
    start_time: Mapped[float] = mapped_column(Float, nullable=False)
    end_time: Mapped[float] = mapped_column(Float, nullable=False)
    summary: Mapped[Optional[str]] = mapped_column(Text)
    theme: Mapped[Optional[str]] = mapped_column(Text)
    emotional_arc: Mapped[Optional[str]] = mapped_column(Text)
    character_emotional_arcs: Mapped[Optional[dict]] = mapped_column(JSON)
    status: Mapped[str] = mapped_column(String, default="pending")
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now())

    scenes: Mapped[List["Scenes"]] = relationship(back_populates="sequence")


class Scenes(Base):
    """存储场景（由多个镜头/Shot组成）的分析结果"""

    __tablename__ = "scenes"
    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    video_id: Mapped[int] = mapped_column(Integer, ForeignKey("video_meta.id", ondelete="CASCADE"), nullable=False)
    sequence_id: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey("story_sequences.id", ondelete="SET NULL"))
    scene_number: Mapped[int] = mapped_column(Integer, nullable=False)
    start_time: Mapped[float] = mapped_column(Float, nullable=False)
    end_time: Mapped[float] = mapped_column(Float, nullable=False)
    summary: Mapped[Optional[str]] = mapped_column(Text)
    narrative_purpose: Mapped[Optional[str]] = mapped_column(Text)
    status: Mapped[str] = mapped_column(String, default="pending")
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now())

    sequence: Mapped[Optional["StorySequence"]] = relationship(back_populates="scenes")
    shots: Mapped[List["Shots"]] = relationship(back_populates="scene")


class Shots(Base):
    """存储镜头（视频的最小视觉单元）的分析结果"""

    __tablename__ = "shots"
    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    scene_id: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey("scenes.id", ondelete="SET NULL"))
    video_id: Mapped[int] = mapped_column(Integer, ForeignKey("video_meta.id", ondelete="CASCADE"), nullable=False)
    shot_order_id: Mapped[Optional[int]] = mapped_column(Integer, index=True)
    start_time: Mapped[float] = mapped_column(Float, nullable=False)
    end_time: Mapped[float] = mapped_column(Float, nullable=False)
    clip_url: Mapped[Optional[str]] = mapped_column(Text)
    low_quality_clip_url: Mapped[Optional[str]] = mapped_column(Text)
    clip_file_size: Mapped[Optional[int]] = mapped_column(Integer)
    # --- 【核心重构】将所有AI分析结果合并到一个JSON字段 ---
    analysis_data: Mapped[Optional[dict]] = mapped_column(JSON)
    # --- 新增：用于存储阶段9修正后的分析数据 ---
    refined_analysis_data: Mapped[Optional[dict]] = mapped_column(JSON)
    # ---
    dialogue: Mapped[Optional[str]] = mapped_column(Text)
    has_dialogue: Mapped[Optional[bool]] = mapped_column(Boolean, default=False)
    embedding_vector: Mapped[Optional[bytes]] = mapped_column(LargeBinary)
    text_embedding_vector: Mapped[Optional[bytes]] = mapped_column(LargeBinary)  # 新增此行
    characters_present: Mapped[Optional[list]] = mapped_column(JSON)
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now())
    updated_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), onupdate=func.now())
    status: Mapped[str] = mapped_column(String, default="pending")
    error_message: Mapped[Optional[str]] = mapped_column(Text)

    faces: Mapped[List["FaceEmbeddings"]] = relationship(back_populates="shot")
    scene: Mapped[Optional["Scenes"]] = relationship(back_populates="shots")

    __table_args__ = (UniqueConstraint("video_id", "shot_order_id", name="uq_video_shot_order"),)


class DetectedShots(Base):
    """存储由PySceneDetect检测出的原始镜头分割点"""

    __tablename__ = "detected_shots"
    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    video_id: Mapped[int] = mapped_column(Integer, ForeignKey("video_meta.id", ondelete="CASCADE"), nullable=False)
    start_time: Mapped[float] = mapped_column(Float, nullable=False)
    end_time: Mapped[float] = mapped_column(Float, nullable=False)
    start_frame: Mapped[int] = mapped_column(Integer, nullable=False)
    end_frame: Mapped[int] = mapped_column(Integer, nullable=False)
    detection_params: Mapped[Optional[str]] = mapped_column(Text)
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now())


class Characters(Base):
    __tablename__ = "characters"
    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    video_id: Mapped[int] = mapped_column(Integer, ForeignKey("video_meta.id", ondelete="CASCADE"), nullable=False)
    name: Mapped[str] = mapped_column(String, default="未知角色")
    description: Mapped[Optional[str]] = mapped_column(Text)
    face_count: Mapped[int] = mapped_column(Integer, default=0)
    representative_embedding: Mapped[Optional[bytes]] = mapped_column(LargeBinary)
    suggested_name_info: Mapped[Optional[dict]] = mapped_column(JSON)
    source: Mapped[Optional[str]] = mapped_column(String)
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now())


class FaceEmbeddings(Base):
    __tablename__ = "face_embeddings"
    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    shot_id: Mapped[int] = mapped_column(Integer, ForeignKey("shots.id", ondelete="CASCADE"), nullable=False)
    character_id: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey("characters.id", ondelete="SET NULL"))
    embedding: Mapped[bytes] = mapped_column(LargeBinary, nullable=False)
    bounding_box: Mapped[Optional[dict]] = mapped_column(JSON)
    time_offset_in_shot: Mapped[float] = mapped_column(Float, nullable=False)
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now())

    shot: Mapped["Shots"] = relationship(back_populates="faces")


class Research(Base):
    __tablename__ = "research"
    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    video_id: Mapped[int] = mapped_column(Integer, ForeignKey("video_meta.id", ondelete="CASCADE"), nullable=False)
    related_entity: Mapped[str] = mapped_column(String, nullable=False)
    source_url: Mapped[Optional[str]] = mapped_column(Text)
    retrieved_content: Mapped[Optional[str]] = mapped_column(Text)
    ai_summary: Mapped[Optional[str]] = mapped_column(Text)
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now())
    updated_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), onupdate=func.now())
    status: Mapped[str] = mapped_column(String, default="pending")


class Transcripts(Base):
    __tablename__ = "transcripts"
    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    video_id: Mapped[int] = mapped_column(Integer, ForeignKey("video_meta.id", ondelete="CASCADE"), nullable=False)
    start_time: Mapped[float] = mapped_column(Float, nullable=False)
    end_time: Mapped[float] = mapped_column(Float, nullable=False)
    text: Mapped[str] = mapped_column(Text, nullable=False)
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now())


class StageOutputs(Base):
    """存储各阶段的JSON输出数据"""

    __tablename__ = "stage_outputs"

    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    video_id: Mapped[int] = mapped_column(Integer, ForeignKey("video_meta.id", ondelete="CASCADE"), nullable=False)
    stage_number: Mapped[int] = mapped_column(Integer, nullable=False)
    stage_name: Mapped[str] = mapped_column(String, nullable=False)
    output_type: Mapped[str] = mapped_column(
        String, nullable=False
    )  # 例如: "story_outline", "causal_graph", "master_context"
    output_data: Mapped[dict] = mapped_column(JSON, nullable=False)
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now())
    updated_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), onupdate=func.now())

    __table_args__ = (UniqueConstraint("video_id", "stage_number", "output_type", name="uq_video_stage_output"),)


class ScriptEvaluations(Base):
    __tablename__ = "script_evaluations"
    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    video_id: Mapped[int] = mapped_column(Integer, ForeignKey("video_meta.id", ondelete="CASCADE"), unique=True)
    evaluation_data: Mapped[dict] = mapped_column(JSON, nullable=False)
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now())


class ProcessingStatus(Base):
    __tablename__ = "processing_status"
    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    video_id: Mapped[int] = mapped_column(Integer, ForeignKey("video_meta.id", ondelete="CASCADE"), nullable=False)
    stage_number: Mapped[int] = mapped_column(Integer, nullable=False)
    stage_name: Mapped[str] = mapped_column(String, nullable=False)
    status: Mapped[str] = mapped_column(String, default="pending")
    started_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True))
    completed_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True))
    error_message: Mapped[Optional[str]] = mapped_column(Text)
    progress_info: Mapped[Optional[str]] = mapped_column(Text)
    __table_args__ = (UniqueConstraint("video_id", "stage_number"),)


class DatabaseManager:
    """数据库管理器 (基于 SQLAlchemy ORM)"""

    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)
        try:
            connection = engine.connect()
            connection.close()
            self.logger.info("✅ 成功连接到 PostgreSQL 数据库。")
        except Exception as e:
            self.logger.error(f"无法连接到 PostgreSQL 数据库: {e}")
            raise

    @contextmanager
    def get_session(self) -> Iterator[Session]:
        session = SessionLocal()
        try:
            yield session
            session.commit()
        except Exception:
            session.rollback()
            raise
        finally:
            session.close()

    def create_video_record(
        self, video_name: str, total_duration: float, file_hash: str, fps: float, input_file_path: Optional[str] = None
    ) -> int:
        with self.get_session() as session:
            new_video = VideoMeta(
                video_name=video_name,
                total_duration=total_duration,
                fps=fps,
                input_file_path=input_file_path,
                file_hash=file_hash,
            )
            session.add(new_video)
            session.flush()
            video_id = typing_cast(int, new_video.id)
            return video_id

    def initialize_stage_statuses(self, video_id: int, stages_info: Dict[int, str]):
        with self.get_session() as session:
            for stage_num, stage_name in stages_info.items():
                new_status = ProcessingStatus(video_id=video_id, stage_number=stage_num, stage_name=stage_name)
                session.add(new_status)
            self.logger.info(f"已为视频ID {video_id} 初始化 {len(stages_info)} 个阶段的状态记录。")

    def sync_stage_statuses(self, video_id: int, stages_info: Dict[int, str]):
        """
        同步指定视频的阶段状态。
        如果数据库中的阶段少于代码中定义的阶段，则为该视频补上缺失的阶段记录。
        """
        with self.get_session() as session:
            # 查询该视频已有的所有阶段编号
            existing_stages_query = session.query(ProcessingStatus.stage_number).filter_by(video_id=video_id)
            existing_stages = {row[0] for row in existing_stages_query}

            # 找出代码中定义了但数据库中缺失的阶段
            missing_stages = set(stages_info.keys()) - existing_stages

            if not missing_stages:
                # self.logger.debug(f"视频ID {video_id} 的阶段状态已是最新，无需同步。")
                return

            # 为所有缺失的阶段创建新的状态记录
            new_statuses = []
            for stage_num in sorted(list(missing_stages)):  # 按顺序添加
                stage_name = stages_info.get(stage_num, "未知阶段")
                new_statuses.append(ProcessingStatus(video_id=video_id, stage_number=stage_num, stage_name=stage_name))

            if new_statuses:
                session.add_all(new_statuses)
                self.logger.info(f"已为视频ID {video_id} 同步并添加了 {len(new_statuses)} 个新的阶段状态记录。")

    def get_video_by_id(self, video_id: int) -> Optional[Dict[str, Any]]:
        with self.get_session() as session:
            video = session.query(VideoMeta).filter(VideoMeta.id == video_id).first()
            return {c.name: getattr(video, c.name) for c in video.__table__.columns} if video else None

    def get_video_by_hash(self, file_hash: str) -> Optional[Dict[str, Any]]:
        with self.get_session() as session:
            video = session.query(VideoMeta).filter(VideoMeta.file_hash == file_hash).first()
            return {c.name: getattr(video, c.name) for c in video.__table__.columns} if video else None

    def get_all_videos(self) -> list[Dict[str, Any]]:
        with self.get_session() as session:
            videos = session.query(VideoMeta).order_by(VideoMeta.created_at.desc()).all()
            return [{c.name: getattr(v, c.name) for c in v.__table__.columns} for v in videos]

    def get_stage_status(self, video_id: int, stage_number: int) -> Optional[Dict[str, Any]]:
        with self.get_session() as session:
            status = session.query(ProcessingStatus).filter_by(video_id=video_id, stage_number=stage_number).first()
            return {c.name: getattr(status, c.name) for c in status.__table__.columns} if status else None

    def update_stage_status(
        self,
        video_id: int,
        stage_number: int,
        status: str,
        error_message: Optional[str] = None,
        progress_info: Optional[str] = None,
    ):
        with self.get_session() as session:
            record = session.query(ProcessingStatus).filter_by(video_id=video_id, stage_number=stage_number).first()
            if record:
                record.status = status
                record.progress_info = progress_info
                if status == "processing":
                    record.started_at = datetime.now()
                elif status in ["completed", "failed"]:
                    record.completed_at = datetime.now()
                    record.error_message = error_message

    def reset_stage_status(self, video_id: int, stage_number: int) -> bool:
        with self.get_session() as session:
            record = session.query(ProcessingStatus).filter_by(video_id=video_id, stage_number=stage_number).first()
            if record:
                record.status = "pending"
                record.started_at = None
                record.completed_at = None
                record.error_message = None
                record.progress_info = None
                return True
            return False

    def save_detected_shots(self, video_id: int, shots: List[Tuple[float, float, int, int]], params: str):
        with self.get_session() as session:
            session.query(DetectedShots).filter_by(video_id=video_id).delete()
            for start_time, end_time, start_frame, end_frame in shots:
                new_shot = DetectedShots(
                    video_id=video_id,
                    start_time=start_time,
                    end_time=end_time,
                    start_frame=start_frame,
                    end_frame=end_frame,
                    detection_params=params,
                )
                session.add(new_shot)

    def create_initial_shot_records(
        self, video_id: int, parsed_shots: List[Tuple[float, float, int, int]], file_hash: str, clips_dir: Path
    ):
        """根据scenedetect的CSV结果，在数据库中创建初始的镜头记录。"""
        with self.get_session() as session:
            # 清理旧记录
            session.query(Shots).filter_by(video_id=video_id).delete()
            session.flush()

            new_shots = []
            for i, (start, end, _, _) in enumerate(parsed_shots):
                clip_path = clips_dir / f"{file_hash}-shot-{i + 1:04d}.mp4"
                new_shots.append(
                    Shots(
                        video_id=video_id,
                        start_time=start,
                        end_time=end,
                        clip_url=str(clip_path.resolve()),
                        status="pending",
                    )
                )
            session.add_all(new_shots)
            self.logger.info(f"已为视频ID {video_id} 创建 {len(new_shots)} 条初始镜头记录。")

    def get_long_shots(self, video_id: int, threshold: float) -> List[Dict[str, Any]]:
        """获取时长超过阈值的所有镜头。"""
        with self.get_session() as session:
            long_shots_query = (
                session.query(Shots.id, Shots.start_time, Shots.end_time, Shots.clip_url)
                .filter(Shots.video_id == video_id, (Shots.end_time - Shots.start_time) > threshold)
                .all()
            )

            return [
                {
                    "id": shot.id,
                    "start_time": shot.start_time,
                    "end_time": shot.end_time,
                    "clip_url": shot.clip_url,
                }
                for shot in long_shots_query
            ]

    def get_shots_to_analyze(self, video_id: int, force_level: Optional[str]) -> List[Dict[str, Any]]:
        """获取需要进行AI分析的镜头列表。"""
        with self.get_session() as session:
            query = session.query(Shots).filter(Shots.video_id == video_id)
            if not force_level:
                query = query.filter(Shots.status != "completed")

            shots_to_process = query.all()
            return [
                {
                    "id": shot.id,
                    "start": shot.start_time,
                    "end": shot.end_time,
                    "path": Path(shot.clip_url) if shot.clip_url else None,
                }
                for shot in shots_to_process
                if shot.clip_url
            ]

    def create_sub_shot_record(
        self, video_id: int, start_time: float, end_time: float, clip_path: str
    ) -> Optional[int]:
        """为分割后的子镜头创建新的数据库记录。"""
        with self.get_session() as session:
            new_shot = Shots(
                video_id=video_id,
                start_time=start_time,
                end_time=end_time,
                clip_url=clip_path,
                status="pending",
            )
            session.add(new_shot)
            session.flush()
            return new_shot.id

    def delete_shot_record_by_id(self, shot_id: int):
        """根据ID删除一个镜头记录。"""
        with self.get_session() as session:
            session.query(Shots).filter_by(id=shot_id).delete()

    def update_shot_analysis_result(
        self,
        shot_id: int,
        low_quality_clip_url: str,
        visual_analysis: Dict[str, Any],
        text_embedding_vector: Optional[np.ndarray],
    ):
        """更新单个镜头的分析结果。"""
        with self.get_session() as session:
            update_data = {
                "low_quality_clip_url": low_quality_clip_url,
                "analysis_data": visual_analysis,
                "text_embedding_vector": text_embedding_vector.tobytes() if text_embedding_vector is not None else None,
                "status": "completed",
            }
            session.query(Shots).filter_by(id=shot_id).update(update_data, synchronize_session=False)  # type: ignore

    def update_shot_error_status(self, shot_id: int, error_message: str):
        """更新单个镜头的错误状态。"""
        with self.get_session() as session:
            session.query(Shots).filter_by(id=shot_id).update(  # type: ignore
                {"status": "failed", "error_message": error_message}, synchronize_session=False
            )

    def get_all_shots_for_analysis(self, video_id: int) -> List[Dict[str, Any]]:
        with self.get_session() as session:
            # 【核心修改】通过JOIN直接获取每个镜头所属场景的 scene_number
            results = (
                session.query(Shots, Scenes.scene_number)
                .outerjoin(Scenes, Shots.scene_id == Scenes.id)
                .filter(Shots.video_id == video_id, Shots.status == "completed")
                .order_by(Shots.start_time.asc())
                .all()
            )

            shots_list = []
            for shot_obj, scene_num in results:
                shot_dict = {c.name: getattr(shot_obj, c.name) for c in shot_obj.__table__.columns}
                shot_dict["scene_number"] = scene_num  # 将 scene_number 添加到字典中
                shots_list.append(shot_dict)
            return shots_list

    def get_shots_by_clip_ids(self, video_id: int, clip_ids: List[str]) -> Dict[str, Dict[str, Any]]:
        """根据 clip_id 列表，高效地获取镜头信息字典。"""
        from sqlalchemy import or_

        if not clip_ids:
            return {}

        with self.get_session() as session:
            # 从 clip_id 中提取文件名用于查询
            filenames = [Path(cid).name for cid in clip_ids]

            # 使用 like 查询来匹配 clip_url 的结尾部分
            query = session.query(Shots).filter(
                Shots.video_id == video_id, or_(*[Shots.clip_url.like(f"%{fn}") for fn in filenames])
            )

            shots = query.all()

            # 构建一个以 clip_id (文件名) 为键的字典，以便快速查找
            shot_map = {}
            for shot in shots:
                if shot.clip_url:
                    shot_filename = Path(shot.clip_url).name
                    shot_dict = {c.name: getattr(shot, c.name) for c in shot.__table__.columns}
                    shot_map[shot_filename] = shot_dict

            return shot_map

    def get_shots_by_order_ids(self, video_id: int, order_ids: List[int]) -> List[Dict[str, Any]]:
        """根据 shot_order_id 列表，高效地获取镜头信息列表。"""
        if not order_ids:
            return []
        with self.get_session() as session:
            shots = session.query(Shots).filter(Shots.video_id == video_id, Shots.shot_order_id.in_(order_ids)).all()
            # 保持原始顺序
            shot_map = {s.shot_order_id: {c.name: getattr(s, c.name) for c in s.__table__.columns} for s in shots}
            return [shot_map[oid] for oid in order_ids if oid in shot_map]

    def get_shots_for_scene(self, scene_id: int) -> List[Dict[str, Any]]:
        """获取属于特定场景的所有镜头"""
        with self.get_session() as session:
            shots = session.query(Shots).filter_by(scene_id=scene_id).order_by(Shots.start_time.asc()).all()
            return [{c.name: getattr(s, c.name) for c in s.__table__.columns} for s in shots]

    def get_shots_with_character_names_for_scene_grouping(self, video_id: int) -> List[Dict[str, Any]]:
        with self.get_session() as session:
            shots = self.get_all_shots_for_analysis(video_id)
            characters = session.query(Characters).filter(Characters.video_id == video_id).all()
            character_map = {char.id: char.name for char in characters}
            for shot in shots:
                character_ids = shot.get("characters_present") or []
                character_names = [character_map.get(cid) for cid in character_ids if character_map.get(cid)]
                shot["character_names_str"] = (
                    ", ".join(name for name in character_names if name is not None) if character_names else ""
                )
            return shots

    def update_video_global_analysis(self, video_id: int, global_summary: str, key_themes: str):
        with self.get_session() as session:
            video = session.query(VideoMeta).filter_by(id=video_id).first()
            if video:
                video.global_summary = global_summary
                video.key_themes = key_themes
                video.status = "analyzed"

    def clear_shot_analysis_data(self, video_id: int):
        with self.get_session() as session:
            session.query(Shots).filter_by(video_id=video_id).delete()

    def clear_global_analysis_data(self, video_id: int):
        with self.get_session() as session:
            video = session.query(VideoMeta).filter_by(id=video_id).first()
            if video:
                video.global_summary = None
                video.key_themes = None
                video.status = "analyzed"

    def get_characters_for_naming(self, video_id: int) -> List[Dict[str, Any]]:
        with self.get_session() as session:
            distinct_face_per_shot_sq = (
                session.query(
                    FaceEmbeddings.id,
                    FaceEmbeddings.shot_id,
                    FaceEmbeddings.character_id,
                    func.row_number()
                    .over(
                        partition_by=(FaceEmbeddings.character_id, FaceEmbeddings.shot_id), order_by=FaceEmbeddings.id
                    )
                    .label("rn"),
                )
                .join(Shots)
                .filter(Shots.video_id == video_id)
                .subquery()
            )
            limit = settings.NAMING_FACE_COUNT
            limited_faces_sq = (
                session.query(
                    distinct_face_per_shot_sq.c.id.label("face_id"),
                    distinct_face_per_shot_sq.c.character_id,
                    func.row_number()
                    .over(
                        partition_by=distinct_face_per_shot_sq.c.character_id,
                        order_by=func.md5(cast(distinct_face_per_shot_sq.c.shot_id, String)),
                    )
                    .label("rn_char"),
                )
                .filter(distinct_face_per_shot_sq.c.rn == 1)
                .subquery()
            )
            results = (
                session.query(
                    Characters.id.label("character_id"),
                    Characters.name.label("character_name"),
                    Characters.face_count,
                    Characters.suggested_name_info,
                    Shots.clip_url,
                    FaceEmbeddings.time_offset_in_shot,
                    FaceEmbeddings.bounding_box,
                    # --- 【核心修改】使用 COALESCE 优先获取修正后的数据 ---
                    coalesce(Shots.refined_analysis_data, Shots.analysis_data)["visual_description"]
                    .as_string()
                    .label("visual_description"),
                    coalesce(Shots.refined_analysis_data, Shots.analysis_data)["main_action"]
                    .as_string()
                    .label("action"),
                    Shots.dialogue,
                )
                .join(limited_faces_sq, Characters.id == limited_faces_sq.c.character_id)
                .join(FaceEmbeddings, FaceEmbeddings.id == limited_faces_sq.c.face_id)
                .join(Shots, FaceEmbeddings.shot_id == Shots.id)
                .filter(Characters.video_id == video_id)
                .filter(Characters.name.like("未知角色 %"))
                .filter(limited_faces_sq.c.rn_char <= limit)
                .order_by(Characters.face_count.desc(), Characters.id, limited_faces_sq.c.rn_char)
                .all()
            )
            return [row._asdict() for row in results]

    def get_naming_info_for_character(self, video_id: int, character_id: int) -> List[Dict[str, Any]]:
        """为指定的单个角色ID获取用于命名的详细信息（包括代表性人脸）。"""
        with self.get_session() as session:
            # 这个查询逻辑与 get_characters_for_naming 非常相似，但核心区别在于
            # 它不按“未知角色”过滤，而是直接按传入的 character_id 过滤。
            distinct_face_per_shot_sq = (
                session.query(
                    FaceEmbeddings.id,
                    FaceEmbeddings.shot_id,
                    FaceEmbeddings.character_id,
                    func.row_number()
                    .over(
                        partition_by=(FaceEmbeddings.character_id, FaceEmbeddings.shot_id), order_by=FaceEmbeddings.id
                    )
                    .label("rn"),
                )
                .join(Shots)
                .filter(Shots.video_id == video_id)
                .subquery()
            )
            limit = settings.NAMING_FACE_COUNT
            limited_faces_sq = (
                session.query(
                    distinct_face_per_shot_sq.c.id.label("face_id"),
                    distinct_face_per_shot_sq.c.character_id,
                    func.row_number()
                    .over(
                        partition_by=distinct_face_per_shot_sq.c.character_id,
                        order_by=func.md5(cast(distinct_face_per_shot_sq.c.shot_id, String)),
                    )
                    .label("rn_char"),
                )
                .filter(distinct_face_per_shot_sq.c.rn == 1)
                .subquery()
            )
            results = (
                session.query(
                    Characters.id.label("character_id"),
                    Characters.name.label("character_name"),
                    Characters.face_count,
                    Characters.suggested_name_info,
                    Shots.clip_url,
                    FaceEmbeddings.time_offset_in_shot,
                    FaceEmbeddings.bounding_box,
                    # --- 【核心修改】使用 COALESCE 优先获取修正后的数据 ---
                    coalesce(Shots.refined_analysis_data, Shots.analysis_data)["visual_description"]
                    .as_string()
                    .label("visual_description"),
                    coalesce(Shots.refined_analysis_data, Shots.analysis_data)["main_action"]
                    .as_string()
                    .label("action"),
                    Shots.dialogue,
                )
                .join(limited_faces_sq, Characters.id == limited_faces_sq.c.character_id)
                .join(FaceEmbeddings, FaceEmbeddings.id == limited_faces_sq.c.face_id)
                .join(Shots, FaceEmbeddings.shot_id == Shots.id)
                .filter(Characters.video_id == video_id)
                .filter(Characters.id == character_id)  # <-- 核心过滤条件
                .filter(limited_faces_sq.c.rn_char <= limit)
                .order_by(Characters.id, limited_faces_sq.c.rn_char)
                .all()
            )
            return [row._asdict() for row in results]

    def get_structured_narrative_for_scripting(self, video_id: int) -> str:
        """
        为剧本创作阶段，从数据库中提取一个结构化的、场景级别的叙事大纲。
        格式为：序列 -> 场景摘要。
        """
        from sqlalchemy.orm import joinedload

        self.logger.info(f"正在为视频ID {video_id} 构建场景级别的叙事结构...")

        with self.get_session() as session:
            # 预加载所有相关的场景，以减少数据库查询次数
            sequences = (
                session.query(StorySequence)
                .options(joinedload(StorySequence.scenes))
                .filter(StorySequence.video_id == video_id)
                .order_by(StorySequence.sequence_number)
                .all()
            )

            if not sequences:
                return "没有可用的序列分析数据。"

            narrative_lines = []
            for seq in sequences:
                narrative_lines.append(f"--- 序列 {seq.sequence_number}: {seq.theme} ---")
                narrative_lines.append(f"   序列摘要: {seq.summary}")

                # 对场景按编号排序
                sorted_scenes = sorted(seq.scenes, key=lambda s: s.scene_number)
                for scene in sorted_scenes:
                    # 【核心修改】只包含场景摘要，不再深入到镜头细节
                    narrative_lines.append(f"  - 场景 {scene.scene_number}: {scene.summary}")

            return "\n".join(narrative_lines)

    def get_all_scene_summaries_for_event_detection(self, video_id: int) -> List[Dict[str, Any]]:
        """获取所有场景的摘要信息，用于事件识别阶段。"""
        with self.get_session() as session:
            scenes = (
                session.query(Scenes).filter_by(video_id=video_id, status="completed").order_by(Scenes.start_time).all()
            )
            # 获取所有角色的ID到名字的映射
            characters = session.query(Characters).filter_by(video_id=video_id).all()
            character_map = {char.id: char.name for char in characters}

            results = []
            for s in scenes:
                # 聚合该场景下所有镜头的角色ID
                shot_character_ids = [char_id for shot in s.shots for char_id in (shot.characters_present or [])]
                # 去重并转换为角色名字
                present_character_names = sorted(
                    list(set(character_map.get(cid, "未知") for cid in shot_character_ids))
                )

                results.append(
                    {
                        "scene_number": s.scene_number,
                        "summary": s.summary,
                        "narrative_purpose": s.narrative_purpose,
                        "characters_present": present_character_names,
                    }
                )
            return results

    def get_all_scenes_with_characters(self, video_id: int) -> List[Dict[str, Any]]:
        """获取所有场景，并聚合每个场景中出现的所有角色的ID和名字。"""
        with self.get_session() as session:
            scenes = (
                session.query(Scenes)
                .filter_by(video_id=video_id, status="completed")
                .order_by(Scenes.scene_number)
                .all()
            )
            characters = session.query(Characters).filter_by(video_id=video_id).all()
            character_map = {char.id: char.name for char in characters}

            results = []
            for s in scenes:
                all_character_ids_in_scene = set()
                for shot in s.shots:
                    if shot.characters_present:
                        all_character_ids_in_scene.update(shot.characters_present)

                scene_dict = {c.name: getattr(s, c.name) for c in s.__table__.columns}
                scene_dict["character_ids"] = sorted(list(all_character_ids_in_scene))
                scene_dict["character_names"] = sorted(
                    [name for cid in all_character_ids_in_scene if (name := character_map.get(cid))]
                )
                results.append(scene_dict)
            return results

    def get_scenes_with_character_ids(self, video_id: int) -> List[Dict[str, Any]]:
        """获取所有场景及其包含的所有去重后的角色ID列表。"""
        with self.get_session() as session:
            scenes = (
                session.query(Scenes).filter_by(video_id=video_id, status="completed").order_by(Scenes.start_time).all()
            )
            results = []
            for scene in scenes:
                # 聚合该场景下所有镜头的角色ID
                all_character_ids_in_scene = set()
                for shot in scene.shots:
                    if shot.characters_present:
                        all_character_ids_in_scene.update(shot.characters_present)

                if all_character_ids_in_scene:
                    results.append(
                        {
                            "scene_number": scene.scene_number,
                            "character_ids": sorted(list(all_character_ids_in_scene)),
                        }
                    )
            return results

    def get_shots_by_scene_number(self, video_id: int, scene_number: int) -> List[Dict[str, Any]]:
        """根据场景编号获取该场景下的所有镜头。"""
        with self.get_session() as session:
            scene = session.query(Scenes).filter_by(video_id=video_id, scene_number=scene_number).first()
            if not scene:
                return []
            # 假设 Scenes 和 Shots 之间有关联关系 'shots'
            return [{c.name: getattr(s, c.name) for c in s.__table__.columns} for s in scene.shots]

    def get_all_research_summaries(self, video_id: int) -> str:
        """获取并合并指定视频的所有研究资料AI摘要。"""
        with self.get_session() as session:
            results = (
                session.query(Research.ai_summary)
                .filter(Research.video_id == video_id, Research.ai_summary.isnot(None))
                .all()
            )
            if not results:
                return "没有可用的研究资料。"
            return "\n\n---\n\n".join([row[0] for row in results])

    def get_unnamed_characters_with_embeddings(self, video_id: int) -> List[Dict[str, Any]]:
        with self.get_session() as session:
            results = (
                session.query(Characters.id, Characters.name, Characters.representative_embedding)
                .filter(Characters.video_id == video_id, Characters.name.like("未知角色%"))
                .all()
            )
            return [
                {"id": r.id, "name": r.name, "embedding": np.frombuffer(r.representative_embedding, dtype=np.float32)}
                for r in results
                if r.representative_embedding is not None
            ]

    def get_all_characters_for_video(self, video_id: int) -> List[Dict[str, Any]]:
        """获取指定视频的所有角色信息 (id, name)。"""
        with self.get_session() as session:
            characters = session.query(Characters).filter_by(video_id=video_id).all()
            return [{"id": char.id, "name": char.name} for char in characters]

    def get_character_details_by_id(self, character_id: int) -> Optional[Dict[str, Any]]:
        """根据数据库主键ID获取单个角色的详细信息。"""
        with self.get_session() as session:
            character = session.query(Characters).filter_by(id=character_id).first()
            if character:
                return {c.name: getattr(character, c.name) for c in character.__table__.columns}
            return None

    def update_character_name(self, character_id: int, new_name: str, source: Optional[str] = None):
        with self.get_session() as session:
            updates = typing_cast(Dict[str, Any], {"name": new_name})
            if source:
                updates["source"] = source
            session.query(Characters).filter_by(id=character_id).update(updates, synchronize_session=False)  # type: ignore
            self.logger.info(f"已将角色ID {character_id} 的名称更新为 '{new_name}'。")

    def update_character_suggestion(
        self, character_id: int, suggestion_data: Dict[str, Any], source: Optional[str] = None
    ):
        with self.get_session() as session:
            updates = typing_cast(Dict[str, Any], {"suggested_name_info": suggestion_data})
            if source:
                updates["source"] = source
            session.query(Characters).filter_by(id=character_id).update(updates, synchronize_session=False)  # type: ignore
            self.logger.debug(f"已为角色ID {character_id} 添加建议: {suggestion_data}")

    def delete_character_and_unassign_faces(self, character_id: int) -> bool:
        """删除一个角色记录，并将其所有关联的人脸设置为未分配状态。"""
        self.logger.warning(f"即将永久删除角色 ID: {character_id} 及其所有关联...")
        with self.get_session() as session:
            try:
                # 步骤 1: 将所有关联此 character_id 的人脸嵌入记录的 character_id 设为 NULL
                unassigned_count = (
                    session.query(FaceEmbeddings)
                    .filter_by(character_id=character_id)
                    .update(  # type: ignore
                        {"character_id": None}, synchronize_session=False
                    )
                )
                self.logger.info(f"已将 {unassigned_count} 个人脸嵌入的 character_id 设置为 NULL。")

                # 步骤 2: 删除角色本身的记录
                deleted_count = session.query(Characters).filter_by(id=character_id).delete()

                if deleted_count > 0:
                    self.logger.info(f"已成功删除角色 ID: {character_id} 的记录。")
                    return True
                else:
                    self.logger.warning(f"在数据库中未找到要删除的角色 ID: {character_id}。")
                    return False
            except Exception as e:
                self.logger.error(f"删除角色 {character_id} 时发生数据库错误: {e}", exc_info=True)
                return False

    def save_transcripts(self, video_id: int, transcripts_data: List[Dict[str, Any]]):
        with self.get_session() as session:
            # 首先，获取该视频的所有镜头时间范围，用于检查重叠。
            shot_time_ranges = session.query(Shots.start_time, Shots.end_time).filter_by(video_id=video_id).all()

            objects_to_save = []
            if not shot_time_ranges:
                self.logger.warning(f"视频ID {video_id} 没有任何镜头数据，无法根据重叠度过滤字幕。将保存所有字幕。")
                objects_to_save = [
                    Transcripts(video_id=video_id, start_time=item["start"], end_time=item["end"], text=item["text"])
                    for item in transcripts_data
                ]
            else:
                for item in transcripts_data:
                    transcript_start, transcript_end = item["start"], item["end"]
                    transcript_duration = transcript_end - transcript_start
                    if transcript_duration <= 0:
                        continue

                    # 检查此字幕是否与任何镜头有显著重叠
                    for shot_start, shot_end in shot_time_ranges:
                        overlap = max(0, min(transcript_end, shot_end) - max(transcript_start, shot_start))
                        if (overlap / transcript_duration) > 0.3:
                            objects_to_save.append(
                                Transcripts(
                                    video_id=video_id,
                                    start_time=transcript_start,
                                    end_time=transcript_end,
                                    text=item["text"],
                                )
                            )
                            break  # 找到一个有效的重叠，移动到下一个字幕项

                self.logger.info(
                    f"根据与镜头的重叠度 (>30%) 过滤后，从 {len(transcripts_data)} 条原始字幕中保留了 {len(objects_to_save)} 条。"
                )

            if objects_to_save:
                session.bulk_save_objects(objects_to_save)
                self.logger.info(f"已为视频ID {video_id} 批量保存 {len(objects_to_save)} 条转录记录。")
            else:
                self.logger.info(f"没有符合重叠度条件的字幕可供保存 (视频ID: {video_id})。")

    def clear_transcription_data(self, video_id: int):
        """清空指定视频的所有转录数据，并清除镜头中的关联对话"""
        with self.get_session() as session:
            # 1. 从 Transcripts 表中删除记录
            session.query(Transcripts).filter_by(video_id=video_id).delete(synchronize_session=False)

            # 2. 将 Shots 表中关联的 dialogue 和 has_dialogue 字段重置
            session.query(Shots).filter_by(video_id=video_id).update(  # type: ignore
                {"dialogue": None, "has_dialogue": False}, synchronize_session=False
            )
            self.logger.info(f"已清理视频ID {video_id} 的旧转录数据和关联镜头对话。")

    def get_transcripts_for_time_range(self, video_id: int, start_time: float, end_time: float) -> str:
        with self.get_session() as session:
            transcripts = (
                session.query(Transcripts.text)
                .filter(
                    Transcripts.video_id == video_id,
                    Transcripts.start_time < end_time,
                    Transcripts.end_time > start_time,
                )
                .order_by(Transcripts.start_time)
                .all()
            )
            return " ".join([t.text for t in transcripts])

    def associate_dialogue_to_shots(self, video_id: int):
        self.logger.info(f"开始为视频ID {video_id} 的所有镜头批量关联对话文本...")
        with self.get_session() as session:
            # 首先将所有镜头的 has_dialogue 重置为 False
            session.query(Shots).filter(Shots.video_id == video_id).update(  # type: ignore
                {"dialogue": None, "has_dialogue": False}, synchronize_session=False
            )

            shots = session.query(Shots).filter(Shots.video_id == video_id).all()
            transcripts = session.query(Transcripts).filter(Transcripts.video_id == video_id).all()
            if not shots or not transcripts:
                self.logger.info("没有找到需要关联对话的镜头或可用的转录文本。")
                return

            updates = []
            for shot in shots:
                best_match_text, max_overlap = None, 0.0
                shot_start, shot_end = typing_cast(float, shot.start_time), typing_cast(float, shot.end_time)
                for transcript in transcripts:
                    overlap_start = max(shot_start, transcript.start_time)
                    overlap_end = min(shot_end, transcript.end_time)
                    overlap_duration = overlap_end - overlap_start
                    if overlap_duration > 0 and overlap_duration > max_overlap:
                        max_overlap, best_match_text = overlap_duration, transcript.text
                if best_match_text:
                    updates.append({"id": shot.id, "dialogue": best_match_text, "has_dialogue": True})

            if updates:
                session.bulk_update_mappings(Shots.__mapper__, updates)
                self.logger.info(f"对话关联完成。共更新了 {len(updates)}/{len(shots)} 个镜头。")
            else:
                self.logger.info("对话关联完成。没有找到任何重叠的字幕进行更新。")

    def save_stage_output(self, video_id: int, stage_number: int, stage_name: str, output_type: str, output_data: dict):
        """保存阶段输出到数据库，并同时在本地保存一个JSON文件副本以供调试。"""
        # 【新增】文件保存逻辑
        video_info = self.get_video_by_id(video_id)
        if video_info and video_info.get("file_hash"):
            video_hash = video_info["file_hash"]
            video_output_dir = settings.OUTPUT_DIR / video_hash
            video_output_dir.mkdir(parents=True, exist_ok=True)
            output_path = video_output_dir / f"{stage_number}-{output_type}.json"
            try:
                import json

                with open(output_path, "w", encoding="utf-8") as f:
                    json.dump(output_data, f, ensure_ascii=False, indent=2)
                self.logger.info(f"阶段输出已保存到本地文件: {output_path}")
            except Exception as e:
                self.logger.error(f"保存本地JSON文件副本失败: {e}", exc_info=True)
        else:
            self.logger.warning("无法获取视频哈希值，跳过保存本地JSON文件。")

        # 【保持不变】数据库保存逻辑
        with self.get_session() as session:
            existing_output = (
                session.query(StageOutputs)
                .filter_by(video_id=video_id, stage_number=stage_number, output_type=output_type)
                .first()
            )

            if existing_output:
                existing_output.output_data = output_data
                existing_output.stage_name = stage_name
            else:
                new_output = StageOutputs(
                    video_id=video_id,
                    stage_number=stage_number,
                    stage_name=stage_name,
                    output_type=output_type,
                    output_data=output_data,
                )
                session.add(new_output)

            self.logger.info(f"已保存阶段{stage_number}的{output_type}输出到数据库")

    def get_stage_output(self, video_id: int, stage_number: int, output_type: str) -> Optional[Dict[str, Any]]:
        """从数据库获取指定阶段的输出数据"""
        with self.get_session() as session:
            output_record = (
                session.query(StageOutputs)
                .filter_by(video_id=video_id, stage_number=stage_number, output_type=output_type)
                .first()
            )
            return output_record.output_data if output_record else None

    def clear_stage_output(self, video_id: int, stage_number: int, output_type: str):
        """清除指定阶段的输出数据，并删除关联的本地JSON文件。"""
        # 【新增】文件清理逻辑
        video_info = self.get_video_by_id(video_id)
        if video_info and video_info.get("file_hash"):
            video_hash = video_info["file_hash"]
            video_output_dir = settings.OUTPUT_DIR / video_hash
            output_path = video_output_dir / f"{stage_number}-{output_type}.json"
            if output_path.exists():
                try:
                    output_path.unlink()
                    self.logger.info(f"已清理本地缓存文件: {output_path}")
                except Exception as e:
                    self.logger.error(f"清理本地JSON文件失败: {e}", exc_info=True)

        # 【保持不变】数据库清理逻辑
        with self.get_session() as session:
            session.query(StageOutputs).filter_by(
                video_id=video_id, stage_number=stage_number, output_type=output_type
            ).delete()
            self.logger.info(f"已清理视频ID {video_id} 阶段 {stage_number} 的 {output_type} 输出。")

    def save_master_script(self, video_id: int, script_data: List[Dict[str, Any]]):
        # 仅写入 stage_outputs
        self.save_stage_output(
            video_id=video_id,
            stage_number=18,  # <-- 修改阶段号
            stage_name="剧本创作 (D2S Rewriter Script)",  # <-- 修改阶段名
            output_type="master_script",
            output_data={"script_beats": script_data},
        )
        self.logger.info(f"已为视频ID {video_id} 保存主旁白文案到 stage_outputs。")

    def get_master_script(self, video_id: int) -> Optional[List[Dict[str, Any]]]:
        data = self.get_stage_output(video_id, 18, "master_script")  # <-- 修改阶段号
        return data.get("script_beats") if data else None

    def clear_master_script(self, video_id: int):
        self.clear_stage_output(video_id, 18, "master_script")  # <-- 修改阶段号
        self.logger.info(f"已清理视频ID {video_id} 的主旁白文案。")

    def search_shots_by_semantic(
        self,
        video_id: int,
        query_vector: np.ndarray,
        top_k: int = 5,
        allowed_shot_ids: Optional[set[int]] = None,
    ) -> List[Dict[str, Any]]:
        """
        通过向量余弦相似度，在指定视频中搜索与查询最相关的镜头。
        """
        with self.get_session() as session:
            # 仅查询包含有效文本嵌入向量的镜头
            query = session.query(Shots).filter(Shots.video_id == video_id, Shots.text_embedding_vector.isnot(None))
            if allowed_shot_ids:
                query = query.filter(Shots.id.in_(allowed_shot_ids))
            shots_with_embeddings = query.all()

            if not shots_with_embeddings:
                self.logger.warning(f"视频ID {video_id} 中未找到任何带有文本向量的镜头。")
                return []

            # 计算相似度
            scored_shots = []
            for idx, shot in enumerate(shots_with_embeddings):
                if not shot.text_embedding_vector:
                    continue
                shot_vector = np.frombuffer(shot.text_embedding_vector, dtype=np.float32)

                if idx < 3:  # 仅前3个镜头打印详细信息
                    self.logger.debug(
                        f"[VecInfo] ShotID:{shot.shot_order_id}  VecDim:{shot_vector.shape} "
                        f"QueryDim:{query_vector.shape}"
                    )

                # 余弦相似度计算
                dot_product = np.dot(query_vector, shot_vector)
                norm_query = np.linalg.norm(query_vector)
                norm_shot = np.linalg.norm(shot_vector)

                if norm_query == 0 or norm_shot == 0:
                    similarity = 0.0
                else:
                    similarity = dot_product / (norm_query * norm_shot)

                scored_shots.append({"shot": shot, "similarity": float(similarity)})

            # 排序并返回top_k
            sorted_shots = sorted(scored_shots, key=lambda x: x["similarity"], reverse=True)

            results = []
            for item in sorted_shots[:top_k]:
                shot_obj = item["shot"]
                shot_dict = {c.name: getattr(shot_obj, c.name) for c in shot_obj.__table__.columns}
                shot_dict["similarity"] = item["similarity"]
                results.append(shot_dict)

            return results

    def save_script_evaluation(self, video_id: int, evaluation_data: Dict[str, Any]):
        with self.get_session() as session:
            existing_eval = session.query(ScriptEvaluations).filter_by(video_id=video_id).first()
            if existing_eval:
                existing_eval.evaluation_data = evaluation_data
            else:
                new_eval = ScriptEvaluations(video_id=video_id, evaluation_data=evaluation_data)
                session.add(new_eval)
            self.logger.info(f"已为视频ID {video_id} 保存剧本评估报告。")

    def get_script_evaluation(self, video_id: int) -> Optional[Dict[str, Any]]:
        with self.get_session() as session:
            eval_record = session.query(ScriptEvaluations).filter_by(video_id=video_id).first()
            return eval_record.evaluation_data if eval_record else None

    def clear_script_evaluation(self, video_id: int):
        with self.get_session() as session:
            session.query(ScriptEvaluations).filter_by(video_id=video_id).delete()
            self.logger.info(f"已清理视频ID {video_id} 的剧本评估报告。")

    def get_shots_for_enrichment(self, video_id: int) -> List[Dict[str, Any]]:
        """获取所有需要进行内容增强的镜头及其完整的上下文信息。"""
        from sqlalchemy.orm import joinedload

        with self.get_session() as session:
            # 使用 joinedload 预加载关联数据，提高效率
            shots = (
                session.query(Shots)
                .options(joinedload(Shots.scene).joinedload(Scenes.sequence))
                .filter(Shots.video_id == video_id)
                .order_by(Shots.id)
                .all()
            )

            # 获取角色ID到名字的映射
            character_map = {
                char.id: char.name
                for char in session.query(Characters.id, Characters.name).filter_by(video_id=video_id).all()
            }

            results = []
            for shot in shots:
                if not shot.scene or not shot.scene.sequence:
                    continue  # 跳过没有完整上下文的镜头

                character_names = [
                    character_map.get(cid) for cid in (shot.characters_present or []) if character_map.get(cid)
                ]

                results.append(
                    {
                        "id": shot.id,
                        "low_quality_clip_url": shot.low_quality_clip_url,
                        # --- 【核心修改】同时提供原始数据和已有的修正数据 ---
                        "original_analysis_json": shot.analysis_data,
                        "refined_analysis_json": shot.refined_analysis_data,
                        # --- 宏观上下文 ---
                        "scene_goal": shot.scene.narrative_purpose,
                        "scene_summary": shot.scene.summary,
                        "sequence_theme": shot.scene.sequence.theme,
                        "sequence_summary": shot.scene.sequence.summary,
                        "character_names": character_names,
                    }
                )
            return results

    def update_shot_refined_analysis(self, updates: List[Dict[str, Any]]):
        """
        批量更新镜头的 refined_analysis_data 字段。
        updates 的格式应为 [{'id': shot_id, 'refined_analysis_data': {...}}, ...]
        """
        if not updates:
            return
        with self.get_session() as session:
            session.bulk_update_mappings(Shots.__mapper__, updates)
            self.logger.info(f"已成功批量更新 {len(updates)} 个镜头的修正后分析数据。")
