"""
AI提示词模板
包含各个阶段使用的提示词模板
"""

import functools

# ------------- 通用指导原则 -------------

# 创作者通用准则
_SOLVER_PRINCIPLES = r"""
## 创作准则
1. 剧本一致性：必须严格符合输入的大纲、角色设定及项目要求；任何与既定事实冲突的情节都视为失败。
2. 真实性与克制：当缺乏足够素材或证据支撑某段剧情时，绝不可凭空捏造；宁可保留占位符或说明缺口。
3. 结构化输出：所有产出须遵循调用处指定的 Pydantic 模型 / JSON 模板，禁止添加额外说明或格式。
4. 自检摘要：在输出内部（若模板包含 meta 字段）先给出对完成度的自我评估与方法草图；然后给出正式内容。
5. **语言**: 你的所有输出都必须使用 **{language}**。
"""

# 评估者通用准则
_VERIFIER_PRINCIPLES = r"""
## 审核标准
1. 非建设性审查：你不负责改写或补全，仅指出问题并分类。
2. 错误分类：
  a. 关键错误 – 情节/角色设定与输入资料矛盾，或逻辑链断裂；一旦出现，指出后停止评阅该链条，但继续检查其他独立段落。
  b. 论证/叙事缺陷 – 结论可能正确，但描述过简、跳跃或缺乏依据；指出不足后**假设此结论成立**继续向后检查。
3. 输出格式：必须使用调用方提供的模板（通常为分节说明或 JSON 评估报告），严禁插入额外修正文本。
"""


def _create_prompt(specific_content: str, principles: str) -> str:
    """将具体任务内容与通用原则结合，形成最终提示词。"""
    transition_text = "\n\n---\n在执行以上任务时，请严格遵守以下准则："
    return f"{specific_content.strip()}{transition_text}\n{principles.strip()}"


# 创建便捷的包装函数
solver_prompt = functools.partial(_create_prompt, principles=_SOLVER_PRINCIPLES)
verifier_prompt = functools.partial(_create_prompt, principles=_VERIFIER_PRINCIPLES)

# ------------- 以上为通用指导原则 -------------


# 【新增】旁白干预指令字典
NARRATION_INTERVENTION_INSTRUCTIONS = {
    "dialogue_heavy": "指令：你的首要任务是保留角色间的原始对话。只在绝对必要时（例如，为了连接两个在时间或空间上不连续的场景）才使用客观旁白(`NARRATOR`)。几乎不使用内心独白(`INNER_MONOLOGUE`)。",
    "balanced": "指令：这是一个强制性要求。你的目标是实现旁白（NARRATOR或INNER_MONOLOGUE）和原声对话（CHARACTER_DIALOGUE）在时长上的大致均衡。根据提供的统计数据，旁白的时间占比应**在40%至60%的合理区间内**。你必须主动地、大量地将非核心、功能性或过渡性的对话改写为简洁的旁白。只保留那些最富戏剧性、最能体现角色性格的高光对话。如果比例严重偏离此区间，将被视为不符合要求。",
    "narration_heavy": "指令：你的唯一目标是创作一个由旁白驱动的解说词。你必须将至少80%的角色对话改写为第三人称叙述(`NARRATOR`)或内心独白(`INNER_MONOLOGUE`)。你不应该直接保留原始对话，除非它是绝对无法替代的、不超过10个词的标志性台词。你的输出应该感觉像一部纪录片或深度电影分析，而不是一部电影剧本。",
}

# --- 新增：创作说明书(Design Doc)生成提示词 ---
CREATIVE_BRIEF_GENERATION_PROMPT = solver_prompt(r"""
# Role & Goal
你是一位顶级的电影制片人和创意总监。你的任务是基于现有的研究资料和用户的宏观创作要求，为一部电影解说短片撰写一份详尽的、结构化的“创作说明书”（即设计文档），并以JSON格式输出。

# Input: Research Data
以下是从多个来源搜集到的关于这部电影的所有研究资料摘要：
---
{research_summary}
---

# Input: User's Creative Brief
以下是用户提出的宏观创作要求：
- **目标受众与传播渠道**: {target_audience}
- **视觉风格与色彩基调**: {visual_style}
- **叙事节奏与剪辑手法**: {narrative_rhythm}
- **成片的目标时长**: {target_duration}
- **目标发布平台**: {platform}
- **叙事视角**: {narration_perspective}

# Task & Rules
1.  **全面提炼**: 将以上所有信息，提炼并整合成一份结构化的创作说明书。这份说明书将作为后续所有AI自动化步骤的"唯一真实之源"。
2.  **【核心】Logline 创作**: 在填充 `logline` 字段时，你必须遵循"雪花写作法"的第二步，撰写一个精确的五句子段落：
    -   第一句：介绍故事的背景、主角和初始状态。
    -   第二句：描述第一个主要的灾难或转折点，打破主角的平衡。
    -   第三句：描述第二个主要的灾难或转折点，使情况更加复杂。
    -   第四句：描述故事的高潮，即第三个也是最终的灾难或转折点。
    -   第五句：描述故事的结局和主角的最终状态。
3.  **ID创造**: 为每个角色创建一个唯一的、易于理解的`character_id`，例如 "char_anna", "char_alex"。
4.  **角色命名**: 除非研究资料中明确提供了官方中文译名，否则所有角色的 `name` 字段必须保持其原文名称（例如：'Lenny Miller' 而不是 '莱尼·米勒'）。
5.  **关系映射**: 在填充角色的`relationships`字段时，确保使用的`character_id`与你为其他角色创建的ID一致。
7.  **【新增】保存视角**: 你必须将用户指定的 `narration_perspective` 值，原封不动地保存到最终输出的 `project_info` 对象中。
8.  **结构化输出**: 你必须严格按照提供的工具（Pydantic模型）来构建你的输出。

请开始你的分析和创作。
""")

# --- D2S: 剧本交互式修订提示词 ---
SCRIPT_REFINEMENT_PROMPT = verifier_prompt(r"""
# Role & Goal
你是一位专业的剧本编辑。你的任务是根据用户的指令，对以下音画节拍进行修订。

# Original Script Beat
---
{original_beat_json}
---

# User's Instruction
"{user_instruction}"

# Task & Rules
1.  **核心任务**: 严格按照用户的指令修订 `visual_description`, `audio_content`, `narration_type` 字段。
2.  **保持上下文**: 在不违背指令的前提下，尽量保持原有的叙事上下文和风格。
3.  **结构化输出**: **只返回**修订后的音画节拍的JSON对象（例如 `{"visual_description": "...", "audio_content": "...", "narration_type": "..."}`），不要包含任何额外的解释、标题或引号。如果某个字段没有变化，则可以不包含在输出中。
4.  **语言**: 你的所有输出都必须使用 **{language}**。
""")

# 新增：基于类型的创作指令
GENRE_INSTRUCTIONS = {
    "action": "风格指令：节奏要极快，多用短句和动作词。强调冲突、危机和高能场面。避免复杂的心理描写，聚焦于生存和战斗的紧张感。",
    "drama": "风格指令：节奏可以放缓，注重人物内心情感的挖掘。强调人物关系的演变、内心的挣扎和关键的对话。语言要有感染力，营造情感深度。",
    "comedy": "风格指令：节奏要轻松明快，语言要幽默、俏皮。强调反差、意外和荒谬感。多使用梗和俏皮话来制造笑点。",
    "personal": "风格指令：风格温馨、纪实，语言要真诚、亲切。注重生活细节和个人感受的表达，与观众建立如同朋友般的连接。",
}


# --- 新增：标语生成提示词 ---
TAGLINE_GENERATION_PROMPT = solver_prompt(r"""
# Role & Goal
你是一位顶级的市场营销专家和广告文案撰写人。你的任务是为一部电影，基于其核心信息，创作一句（一个）极具吸引力的宣传标语（Tagline）。

# Input: Research Summary
以下是关于这部电影的所有研究资料摘要：
---
{research_summary}
---

# Task & Rules
1.  **核心任务**: 基于以上信息，创作一句简洁、有力、能够激发观众好奇心的宣传标语。
2.  **风格**: 标语应该朗朗上口，易于记忆，并能准确传达影片的核心情感或冲突。
3.  **结构化输出**: 你必须使用提供的工具（Pydantic模型）来构建你的输出。
4.  **语言**: 你的所有输出都必须使用 **{language}**。

请开始你的创作。
""")

# --- 统一的、增强版的微观场景分析提示词 V2 ---
SCENE_ANALYSIS_PROMPT = solver_prompt(r"""
# Role & Goal
你是一位电影理论与实践的解构大师、资深的电影学院教授。你的任务是针对所提供的视频片段，进行一次教科书级别的、深入的、结构化的纯视觉分镜头分析。请将你的洞察力聚焦于视听语言如何塑造叙事、传递情感和营造氛围。

# Input
---
### 视频时长: {duration}秒

### 【风格倾向指导】
{genre_instruction}
---

# Task & Deconstruction Framework (任务与解构框架)
你的核心任务是**逐项解构**画面的构成元素，并解读其内在的叙事功能与美学价值。你的分析应当精准、专业，并严格按照提供的工具/格式进行输出。具体要求如下：

-   **`visual_description`**: 提供一段综合性的、叙事性的描述，捕捉画面的整体感觉。
-   **`narrative_function`**: 精准判断这个镜头在故事中的作用是什么？（例如：'建立镜头', '角色引入', '关键转折', '预示'）。
-   **`aesthetic_analysis`**: 分析镜头的美学风格，如色彩、光影如何服务于情感表达。
-   **`people`**: 深入描述人物的状态和微表情。
-   **`setting`**: 描述环境，并分析其与人物心境的关系。
-   **`main_action`**: 描述核心动作，并点出其情节推动作用。
-   **`emotion`**: 提炼镜头传达的核心情绪基调。
-   **`key_objects`**: 列出有象征意义的关键物体。
-   **`on_screen_text`**: 识别所有屏幕上的文字。
-   **`shot_analysis`**: 填充所有技术性镜头语言参数。
-   **`genre_specific_analysis`**: 根据提供的“风格倾向指导”，填写此字段。例如，如果是动作片，则分析其“动作激烈程度”；如果是剧情片，则分析其“潜台词”。

请开始你的专业解构分析。
""")


# 外部资料总结提示词
RESEARCH_SUMMARY_PROMPT = solver_prompt(r"""
你是一个研究助理。请从以下网页内容中，提炼出与 "{entity}" 相关的核心事实、有趣见解或关键引述。

总结应客观、精炼，不超过200字。

网页内容:
{content}

请直接返回总结内容，不需要JSON格式。
""")

# 新增：镜头到场景的聚合提示词
SCENE_GROUPING_PROMPT = solver_prompt(r"""
你是一位经验丰富的剪辑师。你的任务是将以下按时间顺序排列的镜头(Shot)描述，划分成几个具有连贯叙事逻辑的场景(Scene)。一个场景通常发生在同一时间、同一地点，或者在剧情上有密切的联系或连续性。

**特别注意：如果连续的几个镜头拥有完全相同的对白，这极大概率意味着它们属于同一个对话场景，应该被分在同一组。**

镜头列表:
{shot_descriptions}

请分析镜头列表，识别出场景的边界，并使用提供的工具来构建你的分析结果。
对于每个场景，你必须指定 `start_shot_id` 和 `end_shot_id` 来定义其包含的连续镜头范围。这两个ID都必须是“镜头列表”中存在的ID。
**你的所有输出（特别是 `summary` 和 `narrative_purpose`）都必须使用 {language}。**
""")

# 新增：场景到序列的分析提示词
SEQUENCE_ANALYSIS_PROMPT = solver_prompt(r"""
你是一位资深的电影理论家和剧本医生。你的任务是将以下按时间顺序排列的场景(Scene)摘要，组合成更高层次的叙事单元——序列(Sequence)。一个序列由多个场景组成，并围绕一个共同的主题或一个连续的、有始有终的行动段落展开。

场景摘要列表:
{scene_summaries}

请分析场景列表，识别出序列的边界，并使用提供的工具来构建你的分析结果。
**你的所有输出（特别是 `theme`, `summary`, `emotional_arc`）都必须使用 {language}。**
""")

# --- 新增：角色情感弧线分析提示词 ---
CHARACTER_EMOTIONAL_ARC_PROMPT = solver_prompt(r"""
# Role & Goal
你是一位深刻的剧本分析师，擅长洞察人物的内心世界。你的任务是基于一个已定义的叙事序列，分析其中每个主要角色的情感变化弧线。

# Input: Sequence Context
---
### 序列主题: {sequence_theme}

### 序列摘要: {sequence_summary}

### 序列中出现的角色: {character_list}

### 序列包含的场景摘要:
{scene_summaries}
---

# Task & Rules
1.  **聚焦角色**: 对 “序列中出现的角色” 列表中的 **每一位** 角色进行分析。
2.  **描述弧线**: 详细描述每个角色从序列开始到结束的情感变化过程。他们经历了什么？他们的感受如何演变（例如：从困惑到坚定，从喜悦到悲伤）？
3.  **言之有据**: 你的分析必须基于提供的场景摘要内容。
4.  **结构化输出**: 你必须使用提供的工具（Pydantic模型）来构建你的分析结果。
5.  **语言**: 你的所有输出都必须使用 **{language}**。

请开始你的角色情感弧线分析。
""")

# --- 新增：音频分析提示词 ---
AUDIO_ANALYSIS_PROMPT = solver_prompt(r"""
# Role & Goal
你是一位专业的音频分析师。你的任务是仔细聆听提供的音频片段，并对其内容进行结构化的分析。

# Task & Analysis Framework
请根据以下框架，对音频进行全面解构，并使用提供的工具进行输出：
- `transcript`: 将所有可识别的人类对话转录为文字。如果没有对话，请返回空字符串。
- `speaker_tone`: 描述主要说话者的语气、语速和情感状态。
- `music_analysis`: 分析背景音乐的风格、乐器和它所营造的氛围。
- `key_sound_events`: 列出除了对话和音乐之外的关键音效事件（例如：关门声、爆炸声、电话铃声）。

请开始你的音频分析。
""")

# 研究计划生成提示词
RESEARCH_PLAN_PROMPT = solver_prompt(r"""
你是一位资深的研究总监和信息架构师。你的任务是为一部指定的电影制定一个全面、深入的研究计划。

已知信息：
- 电影标题: "{title}"
- 发行年份: {year}
- 导演: {director}
- 主要演员: {actors}

我们已经确定了以下几个必须研究的核心主题，它们将由其他专家处理：
---
**已有的核心主题 (你不需要重复生成这些):**
{existing_topics}
---

# 你的任务
请基于以上信息，为以下**补充类别**，生成2-3个具体的、有深度的、且**不与已有核心主题重复**的研究主题。**所有生成的主题都必须使用 {language}。**

# 补充类别
- **世界观与背景**: 电影设定的时代背景、社会环境、独特的规则或世界观。
- **主创研究**: 导演的标志性风格、主演的表演突破或相关趣闻。
- **评价与遗产**: 电影在特定类型中的地位、对后世的影响、获得的奖项。

请使用提供的工具来构建你的研究计划。
""")

# 新增：孤儿镜头修复专用提示词
ORPHAN_SHOT_FIX_PROMPT = verifier_prompt(r"""
# Role & Goal
你是一位顶级的电影剪辑师，正在进行剪辑审查。你发现了一个在初步剪辑中被遗漏的“孤儿镜头”。你的任务是根据其前后的场景内容，决定这个孤儿镜头最合理的归属。

# Context
- **前一个场景 (Preceding Scene)**: 包含一个或多个镜头，构成一个连贯的叙事单元。
- **孤儿镜头 (Orphan Shot)**: 这是那个被遗漏的、需要被重新安置的镜头。
- **后一个场景 (Succeeding Scene)**: 紧跟在孤儿镜头之后的另一个连贯的叙事单元。

# Input Data
---
### 前一个场景的镜头描述:
{preceding_scene_shots}
---
### **【需要你决策的孤儿镜头】**:
{orphan_shot}
---
### 后一个场景的镜头描述:
{succeeding_scene_shots}
---

# Task
请仔细分析孤儿镜头的内容，并与前后两个场景进行比较。然后做出以下三种决策之一：
1.  `preceding`: 如果孤儿镜头在叙事、时空、或内容上是前一个场景的延续，则应归属于前一个场景。
2.  `succeeding`: 如果孤儿镜头更像是后一个场景的开端，则应归属于后一个场景。
3.  `new_scene`: 如果孤儿镜头与前后场景都无明显关联，内容独立，则它应该自成一个新的单镜头场景。

请使用提供的工具返回你的决策。
""")

# --- D2S Rewriter: 大纲生成提示词 ---
OUTLINE_GENERATION_PROMPT = solver_prompt(r"""
# Role & Goal
你是一位结构感极强的总编剧。你的任务是根据以下经过验证的因果图谱、项目信息、角色设定以及外部研究资料，生成一份详细的、分场景的故事大纲。

# Input: One-Sentence Summary (Tagline)
---
{tagline}
---

# Input: Research Summary
---
{research_summary}
---

# Input: Causal Plot Graph
---
{causal_graph_json}
---

# Input: Project Info & Character Profiles
---
### 项目信息 (Project Info)
{project_info_json}

### 角色设定 (Characters)
{characters_json}

### 叙事视角: {narration_perspective}
---

# Task & Rules
1.  **遍历图谱**: 请使用时间顺序来组织叙事单元的顺序，遍历整个图谱。
2.  **单元定义**: 图中的每一个节点（或一组紧密关联的节点）都应被转换成大纲中的一个叙事单元。
3.  **【核心要求】详细描述**: 对于每个叙事单元的 `summary` 字段，你必须撰写一个详细的段落（约100-150字）。这个段落需要清晰地描述单元的起因、核心事件、关键转折和结局，以及主要角色的行动和反应。目标是让每个单元的描述都足够丰富，能够独立成章。
4.  **角色关联**: 【重要】在填充每个单元的 `characters_present` 字段时，你必须使用**因果图谱节点中已提供的角色名字**，并从“角色设定”中找到对应的 `character_id` 进行填充。
5.  **【核心要求】源场景ID**: 在填充 `candidate_scene_ids` 字段时，你必须从输入事件的 `source_scene_ids` 字段中提取**纯数字ID**，并以整数列表的形式提供，例如 `[1, 2, 3]`。
6.  **保持原文名称**: 在 `summary` 和 `characters_present` 字段中引用角色时，必须使用他们在“角色设定”中的原始名称，不要翻译。
7.  **核对事实**: 在创作过程中，请务必核对研究资料中的事实，确保信息准确无误。
8.  **语言**: 你的所有输出都必须使用 **{language}**。
9.  **结构化输出**: 你必须使用提供的工具（Pydantic模型）来构建你的输出，为每个叙事单元指定编号、目标、涉及角色、设定和候选源场景ID。

请开始你的大纲生成工作。
""")

# --- 新增：剧本创作策略规划提示词 ---
SCRIPT_WRITING_STRATEGY_PROMPT = solver_prompt(r"""
# Role & Goal
你是一位顶级的叙事策略师和 supervising editor。你的任务是基于一个已完成的“故事大纲”，为视频解说制定一份具有独特视角且**完全基于现有素材**的创作策略。

# Core Principles (核心原则)
1.  **【最高优先级】绝对忠于素材**: 你的创作**唯一**的事实来源是下方的 `Input: Story Outline`。你的任务是**解读和规划**，而不是**发明和创作**。
2.  **【硬性约束】禁止杜撰**: 你被严格禁止发明任何故事大纲中不存在的新场景、新动作、具体物品（如“香奈儿五号香水”、“银质十字架项链”）、角色记忆或对话。你的所有策略描述都必须能从故事大纲的 `summary` 中找到直接依据。
3.  **【硬性约束】引用依据**: 在为 `hook_strategy`, `climax_strategy`, `conclusion_strategy` 撰写策略时，你**必须**明确引用你计划使用的叙事单元编号 (`narrative_unit_number`)。例如：“开篇钩子将使用叙事单元 #5，通过展示...来吸引观众”。

# Input: One-Sentence Summary (Tagline)
---
{tagline}
---

# Input: Character Dossiers
---
{dossier_text}
---

# Input: Research Summary
---
{research_summary}
---

# Input: Story Outline (这是你的唯一事实来源)
以下是整个故事的场景大纲：
---
{story_outline_json}
---

# Input: Project Info
以下是项目的核心信息：
---
{project_info_json}

### 角色设定 (Characters)
{characters_json}
---

# Task & Rules
1.  **规划全局叙事结构**: 基于故事大纲，设计一个独特而完整的叙事框架：
    -   **`hook_strategy`**: 从大纲中选择一个或多个叙事单元作为开篇。清晰描述你将如何呈现**这些已存在的场景**来设置悬念、引出核心冲突。
    -   **`climax_strategy`**: 从大纲中识别出代表故事高潮的叙事单元。描述你将如何组织和解读**这些场景**，以最大化其情感冲击力。
    -   **`conclusion_strategy`**: 从大纲中选择合适的叙事单元作为结尾。描述你将如何利用**这些场景**来总结主题、提供余味。
2.  **确定全局基调 (`global_narration_tone`)**: 基于大纲的整体情节、情感走向以及指定的 **{narration_perspective}** 叙事视角，定义一个具体、可执行的旁白风格。例如，如果是第一人称，基调应更主观、个人化；如果是第三人称，则可以更客观、全知。
3.  **语言**: 你的所有输出都必须使用 **{language}**。
4.  **结构化输出**: 你必须使用提供的工具（Pydantic模型）来构建你的输出。

请基于以上指导原则，创作一份完全基于所提供素材的、杜绝任何幻觉的全局创作策略。
""")

# --- 新增：D2S 单场景精修提示词 ---
SCENE_REFINEMENT_PROMPT = solver_prompt(r"""
# Role & Goal
你是一位顶级的剧本医生，擅长优化场景的叙事节奏。你的任务是接收一个旁白比例**严重失衡**的场景剧本，并对其进行**重写**，使其在保留核心信息的前提下，音画节奏更加均衡。

# Input: Problem Diagnosis (问题诊断)
---
### 目标旁白风格: {narration_style_preset}
### 当前场景统计:
- **旁白在总音轨中的时间占比: {narration_ratio:.1%}** (这远高于 'balanced' 风格40%-60%的目标)
---

# Input: Full Story Flow (全局故事流 - 用于理解场景位置)
---
{full_story_flow_json}
---

# Input: Original Scene Outline (原始场景大纲 - 这是“基准真相”)
---
{scene_outline_json}
---

# Input: Unbalanced Script Draft (需要你重写的失衡剧本)
---
{unbalanced_script_json}
---

# Task & Rules
1.  **【核心任务】重写场景**: 你的主要目标是**返回一个完整的、经过你修改后的新剧本**。
2.  **【最高优先级】保持叙事完整性**: 你的所有修改都必须服务于场景大纲中定义的核心故事。绝不能为了降低旁白比例而删除关键情节。
3.  **修改策略**:
    -   **浓缩旁白**: 将冗长的旁白改写得更简洁。
    -   **转换类型**: **寻找机会**将部分旁白(`NARRATOR`)转换为角色的内心独白(`INNER_MONOLOGUE`)或直接对话(`CHARACTER_DIALOGUE`)，以增加戏剧性。
    -   **增加纯视觉节拍**: 如果旁白描述了某个动作，可以考虑将其拆分，创建一个无音频的纯视觉节拍来展示这个动作，让画面自己说话。
4.  **结构化输出**: 你必须使用 `MovieCommentaryScriptResponse` 工具返回完整的、重写后的新剧本节拍列表。
5.  **重新编号**: 在你返回的新剧本中，确保 `beat_number` 是从1开始连续且正确的。
6.  **语言**: 你的所有输出都必须使用 **{language}**。

请开始你的场景精修工作。
""")

# --- 新增：D2S 故事流编排提示词 ---
STORY_FLOW_ARRANGEMENT_PROMPT = solver_prompt(r"""
# Role & Goal
你是一位经验丰富的导演和总编剧，擅长从宏观视角掌控故事的节奏和结构。你的任务是接收一份详细的“故事大纲”和一份高层“创作策略”，然后将它们结合起来，输出一个经过精心编排的、最终的“故事流”。

# Input: High-Level Creative Strategy
这是你必须遵循的全局创作策略：
---
{script_strategy_json}
---

# Input: Detailed Story Outline
这是可供你使用的所有叙事单元（场景）的完整列表：
---
{story_outline_json}
---

# Task & Rules
1.  **【核心任务】编排故事流**: 你的输出是一个**`StoryFlowItem`对象的列表**。你必须根据“创作策略”中定义的 `hook_strategy`, `climax_strategy`,
`conclusion_strategy`，从“故事大纲”中挑选并排列场景，形成一个引人入胜的完整叙事流。
2.  **策略实现**:
    -   **开篇 (Hook)**: 根据 `hook_strategy` 选择一个或多个场景作为故事的开端。
    -   **高潮 (Climax)**: 根据 `climax_strategy` 选择并放置高潮场景。
    -   **结尾 (Conclusion)**: 根据 `conclusion_strategy` 选择收尾场景。
    -   **主体 (Body)**: 将剩余的场景合乎逻辑地填充在开篇、高潮和结尾之间，确保故事的连贯性。
3.  **无需创造**: 你不能创造新的场景，所有使用的场景都必须来自“故事大纲”。你可以选择性地**省略**一些不重要的场景，以使故事更紧凑。
4.  **提供理由**: 对于你放入故事流的**每一个场景**，都必须在 `reasoning` 字段中提供简要的编排理由。例如：“作为开篇钩子，展示主角的双重身份”或“作为高潮部分，揭示核心冲突”。
5.  **ID 复制**: 你必须原封不动地从“故事大纲”中复制 `narrative_unit_number`。
6.  **【硬性约束】唯一性**: 原始故事大纲中的每一个 `narrative_unit_number` 在最终的故事流中**最多只能出现一次**。绝不允许重复使用同一个场景。
7.  **语言**: 你的所有输出都必须使用 **{language}**。
8.  **结构化输出**: 你必须严格使用提供的工具（`StoryFlowResponse`）来构建你的输出。

请开始你的故事流编排工作。
""")

# --- D2S: 剧本质量评估提示词 (REACT-S) ---
SCRIPT_EVALUATION_PROMPT = verifier_prompt(r"""
# Role & Goal
你是一位专业的AI作品质量评估师，负责根据一个结构化的评估框架（REACT-S），对AI生成的剧本进行客观、严谨的评估。

# Input: Ground Truth & Context
以下是作为评估基准的“唯一真实之源”中的项目信息和角色设定：
---
### 项目信息 (Project Info)
{project_info_json}

### 角色设定 (Characters)
{characters_json}
---

# Input: Script to be Evaluated
以下是需要你评估的完整剧本内容：
---
{script_text}
---

# Task & Framework
你的任务是基于以下 REACT-S 框架，对剧本的五个维度进行评分（1-5分）并给出详细理由。

## REACT-S 评估框架:
- **Relevance (关联性)**: 剧本内容与项目核心目标、主题的契合程度。
- **Engagement (吸引力)**: 剧本的节奏、冲突、对白和情感冲击力。
- **Adherence (遵循度)**: 角色行为与预设定的性格、动机的一致性。
- **Coherence (连贯性)**: 故事的逻辑性和情节发展的合理性。
- **Technical-quality (技术性)**: 剧本格式的规范性及对后期制作的指导价值。

请使用提供的工具（Pydantic模型）来构建你的结构化评估报告。**你的所有评估理由都必须使用 {language}。**
""")

# --- D2S Reader: 因果链接推断提示词 ---
CAUSAL_LINK_INFERENCE_PROMPT = solver_prompt(r"""
# Role & Goal
你是一位擅长因果逻辑与深层心理分析的叙事理论家。你的任务是基于以下已识别的事件列表，并参考项目的全局设定（项目信息、角色档案），推断这些事件之间的因果关系，特别是要挖掘行为背后的心理动机。

# Input: Research Summary
---
{research_summary}
---

# Input: Project Info & Character Dossiers
---
### 项目信息 (Project Info)
{project_info_json}

### 角色档案 (Character Dossiers)
{dossier_text}
---

# Input: Narrative Events
以下是已识别的事件列表，请特别注意每个事件附带的`psychological_motivation`字段，它为你提供了分析角色行为的直接线索。
---
{narrative_events_json}
---

# Task & Rules
1.  **推断链接**: 仔细分析每个事件，并找出它们之间所有可能的、合乎逻辑的因果关系。
2.  **【核心】深层因果分析**: 在撰写`causality_description`时，你必须超越表面的“行动-结果”关系。请结合角色档案和事件的心理动机，解释**为什么**一个事件会导致另一个事件的发生。例如，不要只说“A的死亡导致B的复仇”，而要说“由于B对A深厚的忠诚（来自角色档案），A的死亡触发了B强烈的保护欲和复仇动机，从而导致了他的复仇行动。”
3.  **全面覆盖**: 尝试为尽可能多的事件建立联系，但不要凭空捏造。
4.  **语言**: 你的所有输出都必须使用 **{language}**。
5.  **结构化输出**: 你必须严格按照提供的工具（Pydantic模型）来构建你的输出。

请开始你的因果推断工作。
""")

# --- D2S Reader: 事件识别提示词 ---
EVENT_IDENTIFICATION_PROMPT = solver_prompt(r"""
# Role & Goal
你是一位专业的叙事理论家和剧本医生。你的任务是仔细分析以下按时间顺序排列的**场景摘要列表**，并从中识别出推动故事发展的关键“叙事事件”，并评估其重要性。

一个“叙事事件”是一个有意义的、能够推动情节发展的动作或时刻。它可能由一个或多个连续的场景构成。

# Input: Scene Data
---
{scene_data_json}
---

# Task & Rules
1.  **输入理解**: 你的输入是一个JSON数组，其中每个对象代表一个按时间顺序排列的场景，包含`scene_number`, `summary`等关键信息。
2.  **事件粒度**: 每个事件描述必须只包含**一个**核心动作/情节节点。
    - 例如 “Anna 在巴黎开始模特工作” 与 “被介绍给 Oleg” 是两件独立的剧情，应拆分成两个 event。
    - 如果一个场景里出现数个时间、地点或目标明显不同的动作，请分别生成多条事件。
3.  **识别事件**: 从提供的场景摘要中识别出所有关键的叙事事件。
4.  **【核心新增】评估重要性**: 为每个事件评估其`importance_score` (0.0-1.0)。评分标准：
    -   **高分 (0.8-1.0)**: 关键情节转折、核心冲突、角色命运的重大改变。
    -   **中分 (0.4-0.7)**: 推动情节发展的重要步骤、关键信息的揭示、角色关系的重要变化。
    -   **低分 (0.0-0.3)**: 背景铺垫、氛围营造、次要情节。
5.  **【核心新增】分析动机**: 为每个事件分析并填写`psychological_motivation`字段，描述驱动事件发生的核心角色的内在心理状态或动机。
6.  **简洁描述**: 为每个事件提供一个简洁但信息丰富的描述。
7.  **提取角色**: 【重要】在 `characters_present` 字段中，你必须准确地列出在该事件涉及的场景中出现的所有角色名称。
8.  **关联素材**: 【硬性约束】在`source_scene_numbers`字段中，你必须从输入数据中找到构成该事件的源场景，并直接使用它们的 `scene_number` **数字**。例如，如果一个事件由场景2和场景3构成，则此字段应为 `[2, 3]`。
9.  **语言**: 你的所有输出都必须使用 **{language}**。
10. **结构化输出**: 你必须严格按照提供的工具（Pydantic模型）来构建你的输出。

请开始你的分析和提取工作。
""")

# --- D2S Rewriter: 视觉节拍汇编提示词 (替换原 VISUAL_SCRIPT_ASSEMBLY_PROMPT) ---
VISUAL_BEAT_ASSEMBLY_PROMPT = solver_prompt(r"""
# Role & Goal
你是一位严谨的“视觉序列汇编器”（Visual Sequence Assembler）。你的任务是接收一个“叙事单元”及其关联的、来自原始视频的场景数据（Ground Truth），然后将这些源场景**逐一转换**为结构化的纯视觉节拍。

# Core Principles (核心原则)
1.  **【最高优先级】绝对忠于素材**: 你的创作**唯一**的事实来源是 `original_scene_data` 字段。你的任务是转述，不是创作。
2.  **一对一映射**: `original_scene_data` 列表中的**每一个**场景，都必须在输出的 `visual_beats` 列表中有一个对应的 `VisualBeat` 对象。
3.  **禁止杜撰**: 在生成 `VisualBeat` 的 `description` 时，你必须**基于**源场景的 `summary`。你可以进行微小的语法调整使其更流畅，但**绝对禁止**添加任何源摘要中不存在的动作、物体、情感或细节。

# Input: Global Context (全局上下文)
---
### 全局故事流 (Full Story Flow):
{full_story_flow_json}
---

# Input: Creative Direction (创作风格指导)
---
### 全局旁白基调: {global_narration_tone}
### 类型风格指令: {genre_instruction}
---

# Input: Narrative Unit Outline (当前叙事单元大纲)
这是你需要为其创建视觉节拍的叙事单元。**请严格依据 `original_scene_data` 字段进行创作。**
---
{scene_outline_json}
---

# Task & Rules
1.  **【核心任务】生成单一剧本节拍**: 你的输出是一个 `ScriptBeat` 对象的列表，这个列表中**必须只包含一个** `ScriptBeat` 对象。
2.  **填充 `source_narrative_unit_number`**: 你必须从输入的 `scene_outline_json` 中，原封不动地复制 `narrative_unit_number` 的值。
3.  **填充 `beat_number`**: 请将此字段的值固定设置为 `1`。最终的连续编号将由程序在后续步骤中自动完成。
4.  **填充 `visual_beats` 列表**: 在 `ScriptBeat` 内部，遍历 `original_scene_data` 中的每一个场景：
    -   创建一个 `VisualBeat` 对象。
    -   将其 `source_scene_number` 设置为源场景的 `scene_number`。
    -   将其 `description` 设置为对源场景 `summary` 的忠实转述。
6.  **【硬性约束】禁用音频**: `audio_content` 和 `narration_type` 字段必须始终为 `null`。
7.  **语言**: 你的所有输出都必须使用 **{language}**。
8.  **结构化输出**: 你必须严格使用提供的工具（`MovieCommentaryScriptResponse`）来构建你的输出。

请开始你的视觉序列汇编工作。
""")

# --- D2S Rewriter: 单个叙事单元智能修剪提示词 ---
BEAT_TRIM_PROMPT = solver_prompt(r"""
# Role & Goal
你是一位精于取舍的定剪导演（Supervising Editor）。你的任务是接收一个单一的、过长的纯视觉叙事单元（ScriptBeat），并根据明确的时长削减目标，对其进行精确的、外科手术式的修剪。

# Input: The Overlong Beat (需要你修剪的过长节拍)
---
{beat_to_trim_json}
---

# Input: Trimming Goal (修剪目标)
---
- 当前单元时长: {current_duration:.2f} 秒
- 目标削减时长: 约 {trim_goal_seconds:.2f} 秒
---

# Task & Rules
1.  **【核心任务】返回一个修剪后的节拍**: 你的目标通过以下两种方式来达到时长要求：
    -   **方式一 (首选)**: 移除 `visual_beats` 中的部分镜头（即缩短 `selected_shot_order_ids` 列表）。
    -   **方式二 (必要时)**: 如果仅移除镜头无法满足要求，或某个 `visual_beat` 在叙事上相对次要，你可以直接**从 `visual_beats` 列表中移除整个 `visual_beat` 对象**。你每次最多可以酌情删除1-2个场景。
    你必须返回一个**完整的、经过你修改后的新 ScriptBeat 对象**。
2.  **【最高优先级】保护叙事核心**: 在删减时，无论是删镜头还是删场景，都必须保留最能体现该单元核心 `description` 的内容。
3.  **删减策略**:
    -   优先移除那些信息量较低、重复性或纯粹建立氛围的镜头。
    -   在移除整个 `visual_beat` 时，优先选择那些对主线情节推动作用较小的、或者可以被其他节拍内容所涵盖的场景。
    -   必须严格遵守“动作完整性”原则：在移除镜头时，不能破坏一个完整动作的因果链条。
4.  **【硬性约束】** 在你返回的 `ScriptBeat` 对象中，其内部的每一个 `VisualBeat` 对象都**必须**包含 `selected_shot_order_ids` 字段。即使你决定移除一个视觉节拍中的所有镜头，也必须返回一个空的 `selected_shot_order_ids: []` 列表，而不是省略该字段。
5.  **结构化输出**: 你必须使用 `TrimmedBeatResponse` 工具返回完整的、经过修订的 `ScriptBeat` 对象。
6.  **语言**: 你的所有输出都必须使用 **{language}**。

请开始你的精剪工作。
""")

# --- D2S Rewriter: 场景镜头选择提示词 ---
SHOT_SELECTION_FOR_SCENE_PROMPT = solver_prompt(r"""
# Role & Goal
你是一位顶级的电影剪辑师，拥有卓越的镜头感和叙事直觉。你的任务是为一个场景的“视觉剧本”（Visual Beat），从一系列可用的候选镜头中，挑选出一个最能表达其内容的镜头序列。

# Input: Visual Beat Script (视觉剧本)
这是本场景需要用镜头来呈现的核心视觉叙事：
---
{visual_beat_description}
---

# Input: Available Shots (本场景所有可用的候选镜头)
以下是本场景所有可用的候选镜头，已按时间顺序排列：
---
{candidate_shots_json}
---

# Task & Rules
1.  **核心任务**: 从“可用镜头”列表中，挑选出一个子序列。这个子序列必须在视觉上与“视觉剧本”高度匹配。
2.  **叙事连贯**: 确保你选择的镜头序列在视觉上是连贯流畅的，能够清晰地讲述一个小故事。
3.  **【硬性约束】动作完整性 (Action Integrity)**: 你必须保留完整的动作链条。如果一个镜头是某个动作的结果（例如：开枪、关门、倒下），那么你必须也选择展示该动作起因或准备过程的前置镜头。绝对不能只选择高潮瞬间而省略其必要的铺垫镜头。
4.  **保留精华**: 在满足“动作完整性”的前提下，优先选择那些最具信息量、情感最饱满的镜头。你可以省略一些重复或不重要的过渡镜头。
5.  **结构化输出**: 你必须使用提供的工具（Pydantic模型）返回你最终决定的镜头 `shot_order_id` 列表和你的剪辑理由。
6.  **【硬性约束】顺序要求**: 你返回的 `shot_order_id` 列表必须严格按照**数字升序**排列，以保证剪辑的时间线正确。
7.  **语言**: 你的所有输出都必须使用 **{language}**。

请开始你的精剪工作。
""")

# --- 新增：从候选镜头中挑选最佳序列的提示词 ---
SHOT_SELECTION_PROMPT = solver_prompt(r"""
# Role & Goal
你是一位经验丰富的电影剪辑师，拥有卓越的镜头感和叙事直觉。你的任务是为一句给定的旁白，从一系列候选镜头中挑选出最匹配、最具表现力的镜头序列。

# Input
---
### 旁白句子:
"{sentence}"

### 旁白预计朗读时长:
{narration_duration} 秒

### 候选镜头列表 (按相关性粗排):
{detailed_shot_options}
---

# Task & Rules
1.  **理解旁白**: 深刻理解旁白句子的核心内容、情感和节奏。
2.  **评估镜头**: 仔细评估每个候选镜头的完整描述，包括视觉内容、动作、对话和氛围。
3.  **选择序列**: 从候选列表中，挑选一个或多个镜头，组成一个既能完美匹配旁白内容，又能覆盖其时长的镜头序列。
4.  **时长匹配**: 你选择的镜头序列的总时长应约等于或略长于旁白时长。
5.  **提供理由**: 简要说明你为什么选择这个序列，它如何增强旁白的说服力。
6.  **结构化输出**: 你必须使用提供的工具（Pydantic模型）来构建你的输出，只返回选中的镜头顺序ID列表和你的理由。

请开始你的剪辑决策。
""")

# --- D2S Rewriter: 单个叙事单元智能修剪提示词 ---
BEAT_TRIM_PROMPT = solver_prompt(r"""
# Role & Goal
你是一位精于取舍的定剪导演（Supervising Editor）。你的任务是接收一个单一的、过长的纯视觉叙事单元（ScriptBeat），并根据明确的时长削减目标，对其进行精确的、外科手术式的修剪。

# Input: The Overlong Beat (需要你修剪的过长节拍)
---
{beat_to_trim_json}
---

# Input: Trimming Goal (修剪目标)
---
- 当前单元时长: {current_duration:.2f} 秒
- 目标削减时长: 约 {trim_goal_seconds:.2f} 秒
---

# Task & Rules
1.  **【核心任务】返回一个修剪后的节拍**: 你的目标是通过**移除**`visual_beats`中的部分镜头（即缩短`selected_shot_order_ids`列表）来达到时长要求。你必须返回一个**完整的、经过你修改后的新 ScriptBeat 对象**。
2.  **【最高优先级】保护叙事核心**: 在删减时，必须保留最能体现该单元核心`description`的镜头。
3.  **删减策略**:
    -   优先移除那些信息量较低、重复性或纯粹建立氛围的镜头。
    -   必须严格遵守“动作完整性”原则：在移除镜头时，不能破坏一个完整动作的因果链条。
4.  **结构化输出**: 你必须使用 `TrimmedBeatResponse` 工具返回完整的、经过修订的 `ScriptBeat` 对象。
5.  **语言**: 你的所有输出都必须使用 **{language}**。

请开始你的精剪工作。
""")

# --- D2S: 角色信息补全提示词 ---
CHARACTER_COMPLETION_PROMPT = solver_prompt(r"""
# Role & Goal
你是一位严谨的剧本分析师。你的任务是基于已有的创作说明书（Design Doc），为其中提到但尚未定义的角色，补充完整的角色信息。

# Input: Existing Design Doc Context
以下是目前已经生成的创作说明书内容：
---
{existing_design_doc_json}
---

# Task & Rules
1.  **核心任务**: 请为以下 **缺失的角色ID** 列表中的每一个角色，生成详细的角色信息。
    - **缺失的角色ID**: {missing_character_ids}
2.  **保持一致**: 你生成的角色描述和关系，必须与已有的上下文保持逻辑一致。
3.  **角色命名**: 除非研究资料中明确提供了官方中文译名，否则所有新补充角色的 `name` 字段必须保持其原文名称。
4.  **结构化输出**: 你必须使用提供的工具（Pydantic模型）来构建你的输出，只返回这些**新补充**的角色信息列表。
5.  **语言**: 你的所有输出都必须使用 **{language}**。

请开始你的角色信息补全工作。
""")

# --- 新增：D2S 角色关系丰富提示词 ---
RELATIONSHIP_ENRICHMENT_PROMPT = solver_prompt(r"""
# Role & Goal
你是一位专业的剧本分析师，擅长洞察人物的内在动机和动态关系。你的核心任务是深入分析一个详细的叙事大纲，并基于此，对现有的角色设定文件进行审查和扩充，特别是要丰富角色之间的关系描述。

# Input: Existing Character Profiles
这是当前的角色设定文件。请注意，其中一些角色关系可能是缺失的，或者仅仅是基于共现的通用描述（例如，“场景中共同出现”）。
---
{characters_json}
---

# Input: Narrative Outline (Sequences and Scenes)
这是整个故事的叙事大纲，你必须依据它来推断和描述角色间的具体互动和关系演变。
---
{narrative_outline}
---

# Task & Rules
1.  **分析动态关系**: 仔细阅读叙事大纲，理解角色在每个场景和序列中是如何互动的，他们的关系是如何发展的。
2.  **丰富关系描述**: 遍历每一个角色，更新其 `relationships` 列表。
    -   如果一个关系已存在但描述宽泛，请根据故事情节将其变得更具体、更有深度。例如，将“与Oleg共同出现”改为“在Oleg的胁迫下被迫合作”。
    -   如果一个关系在故事中很明显，但在设定中缺失，请补充进去。
3.  **具体化**: 关系描述应反映故事中的具体事件，使用动态的、描述性的语言。例如，优选“在最终对决中背叛了Anna”，而不是“敌人”。
4.  **保持原文名称**: 绝对不能翻译或修改任何角色的 `name` 字段。
5.  **保持结构**: 你的输出必须是完整的、更新后的角色对象列表，严格遵循原始JSON结构。**绝对不能修改 `character_id`**。
6.  **返回完整对象**: 你必须返回包含所有角色（无论是否修改）的完整列表，而不仅仅是发生变动的角色。
7.  **语言**: 所有输出内容必须使用 **{language}**。
8.  **输出格式**: 你的输出必须是一个能够被 `CharacterCompletionResponse` 模型解析的JSON对象。

请开始你的角色关系分析与丰富工作。
""")

# --- D2S: 角色档案从头生成提示词 ---
CHARACTER_DOSSIER_GENERATION_PROMPT = solver_prompt(r"""
# Role & Goal
你是一位资深的编剧和剧本医生。你的任务是基于提供的研究资料和角色设定，为**每一个角色**生成一份详尽的角色档案，深入挖掘其背景、内心冲突和人物弧光。

# Input: Research Summary
---
{research_summary}
---

# Input: Character Profiles (Ground Truth)
---
{character_profiles_text}
---

# Task & Rules
1.  **全面覆盖**: 你必须为 "Input: Character Profiles" 中提到的 **每一个角色** 生成档案。
2.  **深度挖掘**: 档案内容需要超越角色设定的表面信息，结合研究资料进行深度创作。
3.  **【新增】遵循叙事视角**: 你的写作风格必须严格遵循指定的叙事视角：**{narration_perspective}**。如果是 'first_person'，请使用第一人称口吻（例如“我的背景是...”）；如果是 'third_person'，请使用第三人称（例如“他的背景是...”）。
4.  **结构化输出**: 你必须使用提供的工具（Pydantic模型）来构建你的输出，返回一个包含所有角色档案的列表。
5.  **语言**: 你的所有输出都必须使用 **{language}**。

请开始你的角色档案生成工作。
""")

# --- D2S: 角色档案补全/精炼提示词 ---
CHARACTER_DOSSIER_REFINE_PROMPT = solver_prompt(r"""
# Role & Goal
你是一位资深的编剧教练和剧本医生。你的任务是审查并扩展一份已有的角色档案。请使用“基准真相”中的角色设定来丰富“现有档案”，使其更加详尽、深刻，并确保最终输出与基准真相保持一致。

# Input: Research Summary
---
{research_summary}
---

# Input: Ground Truth (来自设计文档的角色设定)
---
{character_profiles_text}
---

# Input: Existing Dossier (需要你优化和补全的上一版档案, JSON格式)
---
{existing_dossier_json}
---

# Task & Rules
1.  **核心任务**: 在"现有档案"的基础上进行扩展和深化，而不是简单重复。请增加新的见解，挖掘潜台词，并充实角色的发展弧光。
2.  **【新增】遵循叙事视角**: 你的写作风格必须严格遵循指定的叙事视角：**{narration_perspective}**。如果是 'first_person'，请使用第一人称口吻；如果是 'third_person'，请使用第三人称。
3.  **保持结构**: 维持原有档案的核心结构，但可以自由地进行改写和扩充。
4.  **结构化输出**: 你必须使用提供的工具（Pydantic模型）返回**完整的、更新后的**角色档案列表。
5.  **语言**: 你的所有输出都必须使用 **{language}**。

请开始你的精炼工作。
""")

# --- D2S: 单场景剧本评估提示词 ---
SCENE_SCRIPT_EVALUATION_PROMPT = verifier_prompt(r"""
# Role & Goal
你是一位严谨的剧本医生和剪辑总监。你的任务是评估AI为单个场景生成的剧本初稿，判断其是否达到了可用于生产的质量标准。

# Input: Narration Style Guide (旁白风格指南)
---
{narration_style_instruction}
---

# Input: Scene Outline (场景大纲 - 这是评估的“基准真相”)
**【评估准则】`original_scene_data` 字段是评估剧本事实一致性的唯一依据。**
---
{scene_outline_json}
---

# Input: Generated Script Draft (需要你评估的剧本初稿)
---
{generated_script_json}
---

# Task & Framework
请根据以下标准进行评估：
1.  **【核心】旁白策略遵循度**: 剧本的旁白和对话比例，是否严格遵循了“旁白风格指南”的要求？
2.  **逻辑遵循度**: 剧本内容是否严格遵循了场景大纲（Scene Outline）的描述？
3.  **叙事质量**: 旁白和对话是否生动、连贯，并成功实现了场景大纲中定义的`scene_goal`？
4.  **技术规范**: 格式是否正确？是否存在明显的逻辑漏洞或常识性错误？

# Output
请使用提供的工具（Pydantic模型）返回你的评估结果。**你的所有评估理由和建议都必须使用 {language}。**
- `score`: 给出1-5的总体评分。
- `is_ready_for_production`: 明确判断此稿件是否可以直接使用（通常score为4或5时为true）。
- `justification`: 简要说明你的评分依据。
- `suggested_improvements`: 【核心】如果稿件不完美（score < 5），请提供**具体、可操作的**修改建议。
""")

# --- D2S: 单场景剧本自我修正提示词 ---
SCENE_SCRIPT_SELF_CORRECTION_PROMPT = solver_prompt(r"""
# Role & Goal
你是一位顶级的、擅长音画结合的电影解说创作者（Screenwriter-Editor），你刚刚收到了关于你初稿的专业反馈。你的任务是基于这些反馈，并重新审视所有原始的创作要求，对单个场景的音画节拍进行**彻底的重写**。

# Overall Creative Direction (原始创作方向)
---
### 项目一句话总结 (Tagline): {tagline}
### 全局旁白基调: {global_narration_tone}

### 类型风格指令:
{genre_instruction}

### 【新增】旁白干预指令:
{narration_intervention_instruction}
---

# Input: Character Profiles & Dossiers (原始角色资料)
以下是本场戏涉及角色的详细设定和档案：
---
### 角色设定 (Characters)
{characters_json}

### 角色档案 (Dossiers)
{dossier_text}
---

# Scene-Specific Strategy & Outline (原始场景策略与大纲)
这是总编剧为你制定的本场戏的具体创作策略和场景大纲。**【最高优先级规则】你必须以 `original_scene_data` 字段中的内容作为剧本修正的唯一事实基础（Ground Truth）。**
---
### 【重要】全局创作策略 (Global Strategy):
{full_script_strategy_json}

### 【重要】全局故事流 (Full Story Flow):
{full_story_flow_json}

### 本场景大纲 (Current Scene Outline):
{scene_outline_json}
---

# Input: First Draft & Evaluation Report (你的初稿及评估报告)
---
### 你的初稿 (First Draft):
{original_script_json}

### 对你初稿的评估报告 (Evaluation Report):
{evaluation_report_json}
---


# Task & Rules
1.  **【核心任务】重写音画节拍**: 你的核心任务是**重写**这个场景的剧本，以系统性地解决“评估报告”中指出的所有问题，同时必须严格遵循所有的原始创作方向和输入材料。
2.  **保持优点**: 在修正问题的同时，请保留初稿中做得好的部分。
3.  **节拍内容填充规则**: (与初稿生成规则完全相同)
    -   `visual_description`: **必须填写**。
    -   `audio_content` 和 `narration_type`: 根据你的创作意图填充旁白、对话或留空。
4.  **【重要】实现节奏感**: 再次强调，你必须有意识地交错使用**旁白节拍**、**原声对话节拍**和**纯视觉节拍**。
5.  **语言**: 你的所有输出都必须使用 **{language}**。
6.  **结构化输出**: 你必须以与初稿完全相同的格式（使用 `MovieCommentaryScriptResponse` 工具）返回**完整的、重写后的**剧本。
7.  **【硬性约束】`audio_content` 与 `narration_type` 必须同时提供或同时留空。** 如果节拍有音频，两者都必须有值；如果节拍是纯视觉，两者都必须为 `null`。
8.  **【内容约束】`audio_content` 字段应只包含需要被朗读的纯文本。不要包含任何如 "旁白:" 或 "角色名(内心独白):" 之类的前缀元数据。**

请开始你的修订工作。
""")

# --- D2S: 粗剪精炼提示词 ---
ROUGH_CUT_REFINEMENT_PROMPT = solver_prompt(r"""
# Role & Goal
你是一位顶级的电影剪辑师。你的任务是为一个已经写好旁白的场景，从一个包含所有可用素材的“粗剪”序列中，挑选出一个最佳的镜头组合（即“精剪”），以达到最强的音画同步和情感冲击力。

# Input: Scene Narration Script
这是本场景需要匹配的旁白/对话内容：
---
{scene_script_text}
---
旁白预估时长: {narration_duration:.2f} 秒

# Input: Rough Cut (Available Candidate Shots)
以下是本场景所有可用的候选镜头，已按时间顺序排列：
---
{candidate_shots_json}
---
候选镜头总时长: {total_candidate_duration:.2f} 秒

# Task & Rules
1.  **核心任务**: 从“粗剪”列表中，挑选出一个子序列。这个子序列必须在视觉上与“旁白脚本”高度匹配。
2.  **时长匹配**: 你挑选出的镜头序列的总时长，应该**约等于或略长于**旁白的预估时长。这是一个硬性约束。
3.  **叙事连贯**: 确保你选择的镜头序列在视觉上是连贯流畅的。
4.  **保留精华**: 优先选择那些最具信息量、情感最饱满、或动作最关键的镜头。
5.  **结构化输出**: 你必须使用提供的工具（Pydantic模型）返回你最终决定的镜头 `shot_order_id` 列表和你的剪辑理由。
6.  **【硬性约束】顺序要求**: 你返回的 `shot_order_id` 列表必须严格按照**数字升序**排列，以保证剪辑的时间线正确。
7.  **【重要提示】候选镜头已过滤**: 提供给你的“候选镜头列表”已经预先过滤，移除了所有在先前节拍中已使用过的镜头。你必须且只能从这个列表中进行选择。
8.  **语言**: 你的所有输出都必须使用 **{language}**。

请开始你的精剪工作。
""")

# --- 新增：镜头内容增强提示词 ---
SHOT_ENRICHMENT_PROMPT = solver_prompt(r"""
# Role & Goal
你是一位资深的电影剪辑师和剧本监督，拥有敏锐的叙事直觉。你的任务是为一个独立的镜头（Shot），基于其**所属的视频片段**和**宏观叙事上下文**，对**整个原始分析报告**进行审查和重写，使其更具叙事感和情感深度。

# Input: Broader Narrative Context (宏观叙事上下文)
---
### 所属序列的主题: {sequence_theme}

### 所属序列的摘要: {sequence_summary}
---
### 所属场景的目标: {scene_goal}

### 所属场景的摘要: {scene_summary}
---

# Input: Original Shot Analysis (需要你增强的原始分析报告)
---
### 镜头中的角色: {characters_present}
### 原始分析报告 (JSON格式):
{original_analysis_json}
---

# Task & Rules
1.  **【核心】视觉优先**: 你的分析必须**首先基于视频片段**的实际内容。提供的文本上下文（序列、场景摘要等）是用来帮助你理解这个片段在故事中的位置和意义，而不是让你脱离画面进行创作。
2.  **核心任务**: **重写并返回一个完整的、更新后的分析对象**。你不仅要优化 `visual_description`，还必须重新审视**所有**字段（如 `people`, `setting`, `main_action`, `emotion`, `shot_analysis` 等），并根据宏观上下文进行修正和深化。
    -   例如，如果场景目标是"展示安娜的孤独"，那么在描述 `people` 时，除了描述外貌，还可以补充"她看起来与周围格格不入"；在 `emotion` 字段，可以从"平静"深化为"平静外表下的落寞"。
3.  **保持事实**: 新的描述不能与原始描述中的核心视觉事实相冲突。你可以增加情感、氛围和叙事层面的解读，但不能虚构画面中不存在的物体或动作。
4.  **叙事性**: 让新的 `visual_description` 读起来像电影解说或小说片段。
5.  **结构化输出**: 你必须使用提供的工具（Pydantic模型）返回**完整的、更新后的**分析结果。
6.  **语言**: 你的所有输出都必须使用 **{language}**。

请开始你的镜头分析增强工作。
""")

# ============== 智能剪辑优化相关提示词 ==============

# --- 智能剪辑质量评估提示词 ---
INTELLIGENT_EDITING_EVALUATION_PROMPT = verifier_prompt(r"""
# Role & Goal
你是一位拥有丰富经验的电影剪辑师和质量控制专家。你的任务是对一个剪辑方案进行全面的质量评估，基于多个维度给出客观、专业的评分和建议。

# Input: Edit Plan (待评估的剪辑方案)
---
### 目标时长范围: {target_duration_range}
### 当前总时长: {current_duration}
### 平台约束: {platform_constraints}
### 节拍总数: {total_beats}

### 详细剪辑计划:
{edit_plan_json}
---

# Input: Original Script Context (原始剧本上下文)
---
{master_script_summary}
---

# Evaluation Dimensions (评估维度)
请对以下5个维度进行评分（1-5分制，5分最高）：

## 1. 时长控制 (Duration Control)
- 评估当前时长是否符合目标范围
- 考虑平台特性（B站/抖音）的最优时长
- 检查是否存在严重的时长偏离

## 2. 素材匹配度 (Material Matching)  
- 评估选择的镜头与音频内容的匹配程度
- 检查视觉描述与旁白的一致性
- 评估素材的完整性和可用性

## 3. 节奏连贯性 (Rhythm Coherence)
- 分析镜头切换的节奏是否合理
- 评估是否存在过于单调或混乱的节奏
- 检查情感高低起伏的处理

## 4. 视觉连贯性 (Visual Coherence)
- 评估镜头间的视觉流畅性
- 检查场景转换的合理性
- 评估整体视觉叙事的完整性

## 5. 音画同步 (Audio-Video Sync)
- 评估音频和视频时长的匹配度
- 检查旁白与视觉内容的同步性
- 评估整体的音画协调性

# Error Classification (错误分类)
对于发现的问题，请分类为：
- **致命错误 (Critical Error)**: 会导致剪辑失败的严重问题（如素材缺失、时长严重超标）
- **优化空间 (Optimization Space)**: 可以改进但不影响基本功能的问题

# Task & Rules
1. **客观评估**: 基于具体数据和标准进行评分，避免主观偏见
2. **问题定位**: 明确指出每个问题的具体位置和原因
3. **建设性建议**: 为每个低分维度提供具体的改进建议
4. **结构化输出**: 使用提供的工具（Pydantic模型）返回评估结果
5. **语言**: 你的所有输出都必须使用 **{language}**

请开始你的专业评估工作。
""")

# --- 智能剪辑节奏优化提示词 ---
RHYTHM_OPTIMIZATION_PROMPT = solver_prompt(r"""
# Role & Goal
你是一位专精剪辑节奏的电影后期专家。你的任务是分析当前的剪辑节奏，并提供具体的优化建议，使其符合目标风格和平台特性。

# Input: Current Rhythm Analysis (当前节奏分析)
---
### 平均镜头时长: {avg_shot_duration}秒
### 时长方差: {duration_variance}
### 节奏评分: {rhythm_score}/5.0
### 镜头总数: {total_shots}

### 目标节奏风格: {target_style}
### 平台特性: {platform}

### 详细镜头时长分布:
{shot_durations_list}
---

# Input: Emotional Context (情感上下文)
---
### 情感强度变化:
{emotional_intensity_curve}
---

# Target Rhythm Patterns (目标节奏模式)
- **fast_paced**: 平均2.0秒/镜头，适合动作和高潮场面
- **balanced**: 平均3.5秒/镜头，适合一般叙事内容  
- **slow_paced**: 平均5.0秒/镜头，适合情感和氛围营造

# Platform Considerations (平台考虑)
- **bilibili**: 偏好稳定节奏，避免过快切换
- **douyin**: 需要紧凑节奏，保持观众注意力

# Task & Rules
1. **节奏诊断**: 分析当前节奏的问题和优势
2. **优化策略**: 提供具体的镜头时长调整建议
3. **情感匹配**: 确保节奏变化与情感强度相匹配
4. **平台适配**: 考虑目标平台的观众习惯
5. **渐进调整**: 避免剧烈的节奏变化，保持自然过渡
6. **结构化输出**: 使用提供的工具返回优化方案
7. **语言**: 你的所有输出都必须使用 **{language}**

请开始你的节奏优化分析。
""")

# --- 智能时长压缩提示词 ---
DURATION_COMPRESSION_PROMPT = solver_prompt(r"""
# Role & Goal
你是一位经验丰富的剪辑师，专门处理时长控制和内容精简。你的任务是在保持核心内容和叙事质量的前提下，将超时的剪辑方案压缩到目标时长范围内。

# Input: Current Edit Plan (当前剪辑方案)
---
### 当前总时长: {current_duration}秒
### 目标时长范围: {target_min}-{target_max}秒
### 需要压缩: {compression_needed}秒
### 压缩比例: {compression_ratio:.2f}

### 详细节拍信息:
{beats_with_priority}
---

# Input: Content Analysis (内容分析)
---
### 核心情节节拍: {core_beats}
### 过渡节拍: {transition_beats}  
### 细节描述节拍: {detail_beats}
---

# Compression Strategies (压缩策略)
1. **优先级保留**: 保留最高优先级的核心情节
2. **智能合并**: 将相似的镜头或主题合并
3. **精简过渡**: 缩短或删除非必要的过渡镜头
4. **节奏加快**: 通过缩短单个镜头时长来加快整体节奏

# Quality Preservation Rules (质量保持原则)
- 保留所有关键的故事转折点
- 维持基本的情感起伏曲线
- 确保视觉叙事的连贯性
- 避免删除具有强情感冲击的镜头

# Task & Rules
1. **分析优先级**: 为每个节拍评估保留的重要性
2. **制定策略**: 选择最适合的压缩方法组合
3. **保持质量**: 确保压缩后的内容仍然完整有趣
4. **时长精确**: 最终时长必须在目标范围内
5. **提供理由**: 为每个保留/删除/修改的决定提供理由
6. **结构化输出**: 使用提供的工具返回压缩方案
7. **语言**: 你的所有输出都必须使用 **{language}**

请开始你的智能压缩工作。
""")

# --- 剪辑审查器提示词 ---
EDITING_REVIEW_PROMPT = verifier_prompt(r"""
# Role & Goal
你是一位资深的剪辑质量审查专家。你的任务是审查另一位AI剪辑师的质量评估结果，确保评估的客观性、一致性和合理性。

# Input: Original Evaluation (待审查的评估结果)
---
### 总评分: {overall_score}/5.0
### 各维度评分:
{dimension_scores}

### 识别的致命问题:
{critical_issues}

### 优化建议:
{optimization_suggestions}
---

# Input: Edit Plan Context (剪辑方案上下文)
---
### 实际数据:
- 当前时长: {actual_duration}秒
- 目标时长: {target_duration}秒  
- 节拍数量: {beats_count}
- 平均置信度: {avg_confidence}
---

# Review Criteria (审查标准)
1. **评分合理性**: 检查评分是否与实际数据相符
2. **问题分类准确性**: 验证致命问题和优化建议的分类是否正确
3. **评估一致性**: 检查各维度评分与总评分的逻辑一致性
4. **标准统一性**: 确保评估标准符合行业规范

# Common Evaluation Errors (常见评估错误)
- 评分过于严苛或宽松
- 将优化建议错误分类为致命问题
- 忽略实际数据，基于主观印象评分
- 各维度权重不合理

# Task & Rules
1. **客观审查**: 基于具体数据验证评估的准确性
2. **标准检验**: 确保评估标准的一致性和合理性
3. **问题识别**: 指出评估中的不合理或错误之处
4. **修正建议**: 为不合理的评估提供修正建议
5. **结构化输出**: 使用提供的工具返回审查结果
6. **语言**: 你的所有输出都必须使用 **{language}**

请开始你的专业审查工作。
""")

# --- 智能镜头选择提示词 ---
INTELLIGENT_SHOT_SELECTION_PROMPT = solver_prompt(r"""
# Role & Goal
你是一位具备丰富经验的电影剪辑师。你的任务是从候选镜头列表中，基于音频内容和视觉描述，智能选择最适合的镜头组合，确保音画高度匹配且叙事流畅。

# Input: Audio Context (音频上下文)
---
### 音频内容: "{audio_content}"
### 音频预估时长: {audio_duration}秒
### 旁白类型: {narration_type}
---

# Input: Visual Context (视觉上下文)  
---
### 视觉描述: "{visual_description}"
### 所属场景: {scene_number}
### 情感强度: {emotional_intensity}/1.0
---

# Input: Candidate Shots (候选镜头列表)
---
{candidate_shots_json}
---

# Selection Criteria (选择标准)
1. **内容匹配**: 镜头视觉内容与音频描述的相关性
2. **时长适配**: 选择的镜头总时长与音频时长的匹配度
3. **情感协调**: 镜头情感氛围与音频情感的一致性
4. **视觉流畅**: 所选镜头间的视觉连贯性
5. **叙事价值**: 镜头对整体叙事的贡献度

# Quality Thresholds (质量阈值)
- 高匹配度: 置信度 ≥ 0.8
- 中等匹配度: 置信度 0.5-0.8  
- 低匹配度: 置信度 < 0.5

# Task & Rules
1. **智能筛选**: 基于多个标准综合评估每个候选镜头
2. **组合优化**: 选择最佳的镜头组合，而非单纯的高分镜头
3. **时长控制**: 确保总时长与音频时长匹配（允许±20%偏差）
4. **备选方案**: 如果高质量选择不足，提供降级备选方案
5. **置信度评估**: 为最终选择的组合提供整体置信度评分
6. **结构化输出**: 使用提供的工具返回选择结果和理由
7. **语言**: 你的所有输出都必须使用 **{language}**

请开始你的智能镜头选择工作。
""")
