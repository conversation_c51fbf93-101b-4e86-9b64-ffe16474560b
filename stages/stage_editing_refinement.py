"""
阶段19：智能精剪 (AI剪辑师)
负责将场景级别的视觉剧本，细化为镜头级别的精剪序列。
"""

import json
from typing import Any, Dict, Optional

from config.prompts import SHOT_SELECTION_FOR_SCENE_PROMPT
from config.schemas import ModelName, ShotSelectionForSceneResponse
from config.settings import settings
from stages.base import BaseStage
from utils.ai_utils import secondary_client


class Stage19AIShotSelection(BaseStage):  # 更改类名以更具体
    """阶段19：AI精剪 (AI Shot Selection)"""  # 更新注释

    def __init__(self, db_manager, video_id):
        super().__init__(db_manager, video_id)
        self.editor_client = secondary_client
        self.editor_client.model = ModelName.DOUBAO_SEED_1_6.value  # 已修改

    @property
    def stage_number(self) -> int:
        return 19

    @property
    def stage_name(self) -> str:
        return "AI精剪 (AI Shot Selection)"  # 更新阶段名

    def check_prerequisites(self) -> tuple[bool, str]:
        """检查前置条件"""
        stage18_status = self.db_manager.get_stage_status(self.video_id, 18)
        if not stage18_status or stage18_status["status"] != "completed":
            return False, "阶段18 (剧本创作) 尚未完成"

        master_script = self.db_manager.get_master_script(self.video_id)
        if not master_script:
            return False, "数据库中未找到主剧本数据"
        return True, ""

    def execute(self, force_level: Optional[str] = None, **kwargs) -> bool:
        """执行从场景到镜头的精剪选择。"""
        output_type = "refined_master_script"
        if force_level:
            self.logger.info(f"强制模式 ({force_level}): 清理旧的精剪后剧本数据。")
            self.db_manager.clear_stage_output(self.video_id, self.stage_number, output_type)

        existing_output = self.db_manager.get_stage_output(self.video_id, self.stage_number, output_type)
        if existing_output:
            self.logger.info("从数据库加载已缓存的精剪后剧本数据。")
            return True

        master_script = self.db_manager.get_master_script(self.video_id)
        if not master_script:
            self.logger.error("未找到来自阶段18的主剧本，无法继续。")
            return False

        self.update_progress("AI剪辑师正在为每个场景挑选最佳镜头...")
        refined_script = []
        try:
            for beat_container in master_script:
                refined_visual_beats = []
                visual_beats_to_process = beat_container.get("visual_beats", [])

                for visual_beat in visual_beats_to_process:
                    refined_beat = self._select_shots_for_visual_beat(visual_beat)
                    # 【核心修改】只有当精剪后的节拍包含有效镜头时，才将其加入列表
                    if refined_beat.get("selected_shot_order_ids"):
                        refined_visual_beats.append(refined_beat)
                    else:
                        self.logger.warning(
                            f"视觉节拍 (源场景: {visual_beat.get('source_scene_number')}) "
                            "在精剪后无有效镜头，将从剧本中移除。"
                        )

                # 【核心修改】只有当叙事单元还包含有效的视觉节拍时，才将其加入最终剧本
                if refined_visual_beats:
                    new_beat_container = beat_container.copy()
                    new_beat_container["visual_beats"] = refined_visual_beats
                    refined_script.append(new_beat_container)
                else:
                    self.logger.warning(
                        f"叙事单元 (ID: {beat_container.get('source_narrative_unit_number')}) "
                        "因所有视觉节拍均无有效镜头而被移除。"
                    )

            self.db_manager.save_stage_output(
                video_id=self.video_id,
                stage_number=self.stage_number,
                stage_name=self.stage_name,
                output_type=output_type,
                output_data={"refined_script_beats": refined_script},
            )
            self.logger.info("✅ AI剪辑师已完成所有场景的镜头挑选。")
            return True
        except Exception as e:
            self.logger.error(f"执行阶段 {self.stage_number} ({self.stage_name}) 时出错: {e}", exc_info=True)
            return False

    def _select_shots_for_visual_beat(self, visual_beat: Dict[str, Any]) -> Dict[str, Any]:
        """为单个VisualBeat调用AI来选择镜头。"""
        scene_num = visual_beat.get("source_scene_number")
        description = visual_beat.get("description")
        if scene_num is None or not description:
            return visual_beat  # 返回原始节拍

        # 1. 获取候选镜头
        candidate_shots = self.db_manager.get_shots_by_scene_number(self.video_id, scene_num)
        if not candidate_shots:
            self.logger.warning(f"场景 {scene_num} 没有任何可用镜头，无法为其挑选。")
            visual_beat["selected_shot_order_ids"] = []
            return visual_beat

        # 2. 准备提示词
        cleaned_shots = [
            {
                "shot_order_id": s["shot_order_id"],
                "duration_sec": s["end_time"] - s["start_time"],
                "visual_description": s.get("visual_description", ""),
                "action": s.get("action", ""),
                "dialogue": s.get("dialogue", ""),
            }
            for s in candidate_shots
        ]

        prompt = SHOT_SELECTION_FOR_SCENE_PROMPT.format(
            visual_beat_description=description,
            candidate_shots_json=json.dumps(cleaned_shots, ensure_ascii=False, indent=2),
            language=settings.SCRIPT_LANGUAGE,
        )

        # 3. 调用AI
        try:
            result = self.editor_client.call_ai_with_tool(prompt, response_model=ShotSelectionForSceneResponse)
            if result and result.selected_shot_order_ids:
                self.logger.info(
                    f"AI为场景 {scene_num} 挑选了 {len(result.selected_shot_order_ids)} 个镜头。理由: {result.justification}"
                )
                visual_beat["selected_shot_order_ids"] = sorted(result.selected_shot_order_ids)
            else:
                self.logger.warning(f"AI未能为场景 {scene_num} 挑选任何镜头，将使用该场景的全部镜头。")
                visual_beat["selected_shot_order_ids"] = [s["shot_order_id"] for s in candidate_shots]
        except Exception as e:
            self.logger.error(f"为场景 {scene_num} 挑选镜头时AI调用失败: {e}。将使用该场景的全部镜头作为后备。")
            visual_beat["selected_shot_order_ids"] = [s["shot_order_id"] for s in candidate_shots]

        return visual_beat
