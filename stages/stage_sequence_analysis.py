"""
阶段8：序列分析 (Scenes to Sequences)
将叙事场景(Scenes)进一步组合成具有主题的序列(Sequences)。
"""

from typing import Any, Dict, List, Optional

from config.prompts import SEQUENCE_ANALYSIS_PROMPT
from config.schemas import ModelName, SequenceAnalysisResponse
from config.settings import settings
from database.models import Scenes, StorySequence
from stages.base import BaseStage
from utils.ai_utils import primary_client


class Stage8SequenceAnalysis(BaseStage):
    """阶段8：序列分析"""

    def __init__(self, db_manager, video_id):
        super().__init__(db_manager, video_id)
        self.analysis_client = primary_client
        self.analysis_client.model = ModelName.DOUBAO_SEED_1_6.value  # 已修改

    @property
    def stage_number(self) -> int:
        return 8

    @property
    def stage_name(self) -> str:
        return "序列分析 (Scenes to Sequences)"  # 更改为英文以保持一致性

    def check_prerequisites(self) -> tuple[bool, str]:
        """检查前置条件"""
        stage7_status = self.db_manager.get_stage_status(self.video_id, 7)
        if not stage7_status or stage7_status["status"] != "completed":
            return False, "阶段7（场景聚合）尚未完成"
        return True, ""

    def execute(self, force_level: Optional[str] = None, **kwargs) -> bool:
        """执行序列分析"""
        if force_level:
            self._clear_data()

        scenes = self._get_scenes()
        if not scenes:
            self.logger.info("没有已聚合的场景(Scenes)可供分析，跳过。")
            return True

        self.update_progress(f"开始将 {len(scenes)} 个场景分析为序列...")
        try:
            scene_summaries = []
            for scene in scenes:
                desc = f"Scene {scene['scene_number']} ({scene['start_time']:.1f}s-{scene['end_time']:.1f}s): {scene['summary']}"
                scene_summaries.append(desc)

            prompt = SEQUENCE_ANALYSIS_PROMPT.format(
                scene_summaries="\n".join(scene_summaries),
                language=settings.SCRIPT_LANGUAGE,
            )
            try:
                sequence_result = self.analysis_client.call_ai_with_tool(
                    prompt, response_model=SequenceAnalysisResponse
                )
            except Exception as e:
                self.logger.error(f"AI序列分析失败: {e}")
                return False

            self._save_sequences([seq.model_dump() for seq in sequence_result.sequences], scenes)
            self.logger.info(f"成功将 {len(scenes)} 个场景分析为 {len(sequence_result.sequences)} 个序列。")
            return True

        except Exception as e:
            self.logger.error(f"执行阶段 {self.stage_number} ({self.stage_name}) 时出错: {e}", exc_info=True)
            return False

    def _clear_data(self):
        """清理此阶段的数据"""
        self.logger.info("强制模式: 正在清理序列分析数据...")
        with self.db_manager.get_session() as session:
            session.query(StorySequence).filter_by(video_id=self.video_id).delete()
            session.query(Scenes).filter_by(video_id=self.video_id).update({"sequence_id": None})

    def _get_scenes(self) -> List[Dict[str, Any]]:
        """从数据库获取所有已聚合的场景"""
        with self.db_manager.get_session() as session:
            scenes_data = (
                session.query(
                    Scenes.id,
                    Scenes.scene_number,
                    Scenes.start_time,
                    Scenes.end_time,
                    Scenes.summary,
                )
                .filter_by(video_id=self.video_id, status="completed")
                .order_by(Scenes.start_time)
                .all()
            )
            # 将 Row 对象转换为字典列表，避免 DetachedInstanceError
            return [
                {
                    "id": s.id,
                    "scene_number": s.scene_number,
                    "start_time": s.start_time,
                    "end_time": s.end_time,
                    "summary": s.summary,
                }
                for s in scenes_data
            ]

    def _analyze_character_arcs_for_sequence(self, sequence: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """为单个序列分析其中角色的情感弧线"""
        from config.prompts import CHARACTER_EMOTIONAL_ARC_PROMPT
        from config.schemas import CharacterEmotionalArcResponse
        from database.models import Characters, Shots

        # 1. 准备所需数据
        scene_numbers = sequence.get("scene_numbers", [])
        if not scene_numbers:
            return None

        # 将所有数据库操作都放在一个会话中以避免DetachedInstanceError
        with self.db_manager.get_session() as session:
            # 从数据库获取此序列中所有场景的详细信息
            scenes_in_seq = (
                session.query(Scenes)
                .filter(Scenes.video_id == self.video_id, Scenes.scene_number.in_(scene_numbers))
                .all()
            )

            if not scenes_in_seq:
                return None

            scene_summaries = "\n".join([f"- Scene {s.scene_number}: {s.summary}" for s in scenes_in_seq])
            all_character_ids = set()

            # 获取所有相关场景的ID
            scene_ids_in_seq = [s.id for s in scenes_in_seq]

            # 【核心修改】一次性查询所有相关场景下的所有镜头，而不是循环查询
            if scene_ids_in_seq:
                shots_in_sequence = session.query(Shots).filter(Shots.scene_id.in_(scene_ids_in_seq)).all()
                for shot in shots_in_sequence:
                    if shot.characters_present:
                        all_character_ids.update(shot.characters_present)

            if not all_character_ids:
                self.logger.info(f"序列 {sequence.get('sequence_number')} 中未发现角色，跳过情感弧线分析。")
                return None

            # 获取角色名称
            characters = session.query(Characters).filter(Characters.id.in_(list(all_character_ids))).all()
            character_map = {char.id: char.name for char in characters}
            character_list = ", ".join([character_map.get(cid, f"ID:{cid}") for cid in all_character_ids])

        # 2. 构建并调用AI (这部分不依赖 session, 保持在 with 块之外)
        prompt = CHARACTER_EMOTIONAL_ARC_PROMPT.format(
            sequence_theme=sequence.get("theme", "N/A"),
            sequence_summary=sequence.get("summary", "N/A"),
            character_list=character_list,
            scene_summaries=scene_summaries,
            language=settings.SCRIPT_LANGUAGE,
        )

        try:
            self.update_progress(f"分析序列 {sequence.get('sequence_number')} 的角色情感弧线...")
            result = self.analysis_client.call_ai_with_tool(prompt, response_model=CharacterEmotionalArcResponse)
            if result and result.character_arcs:
                # 将结果转换为 角色名 -> 情感弧线描述 的字典
                return {arc.character_name: arc.emotional_arc for arc in result.character_arcs}
        except Exception as e:
            self.logger.error(f"为序列 {sequence.get('sequence_number')} 分析角色情感弧线时失败: {e}")

        return None

    def _save_sequences(self, sequences_data: List[Dict[str, Any]], all_scenes: List[Dict[str, Any]]):
        """将AI返回的序列数据存入数据库"""
        scenes_map = {scene["scene_number"]: scene for scene in all_scenes}

        with self.db_manager.get_session() as session:
            for i, seq_info in enumerate(sequences_data):
                # 【核心修改】在此处将序列号添加到字典中
                seq_info["sequence_number"] = i + 1

                scene_numbers = seq_info.get("scene_numbers", [])
                if not scene_numbers:
                    continue

                contained_scenes = [scenes_map[sn] for sn in scene_numbers if sn in scenes_map]
                if not contained_scenes:
                    continue

                start_time = min(s["start_time"] for s in contained_scenes)
                end_time = max(s["end_time"] for s in contained_scenes)

                # --- 【核心修改】调用情感弧线分析 ---
                character_arcs = self._analyze_character_arcs_for_sequence(seq_info)

                new_sequence = StorySequence(
                    video_id=self.video_id,
                    sequence_number=i + 1,
                    start_time=start_time,
                    end_time=end_time,
                    summary=seq_info.get("summary"),
                    theme=seq_info.get("theme"),
                    emotional_arc=seq_info.get("emotional_arc"),
                    character_emotional_arcs=character_arcs,  # 保存新分析的结果
                    status="completed",
                )
                session.add(new_sequence)
                session.flush()

                # 更新所有相关场景的 sequence_id
                scene_ids_to_update = [s["id"] for s in contained_scenes if s["id"] is not None]
                if scene_ids_to_update:  # 只有当有有效ID时才执行更新
                    session.query(Scenes).filter(Scenes.id.in_(scene_ids_to_update)).update(
                        {"sequence_id": new_sequence.id}
                    )
