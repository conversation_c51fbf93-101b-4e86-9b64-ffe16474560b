"""
阶段18：剧本创作 (D2S Rewriter)
第二步：基于大纲和创作策略生成完整剧本
"""

import concurrent.futures
import json
from typing import Any, Dict, List, Optional, Tuple

from config.prompts import (
    GENRE_INSTRUCTIONS,
    NARRATION_INTERVENTION_INSTRUCTIONS,
    SCENE_SCRIPT_EVALUATION_PROMPT,
    SCENE_SCRIPT_SELF_CORRECTION_PROMPT,
    SCRIPT_EVALUATION_PROMPT,
    VISUAL_BEAT_ASSEMBLY_PROMPT,
)
from config.schemas import (
    ModelName,
    MovieCommentaryScriptResponse,
    SceneScriptEvaluationResponse,
    ScriptEvaluationResponse,
)
from config.settings import settings
from stages.base import BaseStage
from utils.ai_utils import primary_client
from utils.shared_state import shutdown_event


class Stage18VisualScriptAssembly(BaseStage):
    """阶段18：视觉剧本汇编 (Visual Script Assembly)"""

    def __init__(self, db_manager, video_id):
        super().__init__(db_manager, video_id)
        self.rewriter_client = primary_client
        self.rewriter_client.model = ModelName.DOUBAO_SEED_1_6.value  # 已修改

    @property
    def stage_number(self) -> int:
        return 18

    @property
    def stage_name(self) -> str:
        return "视觉剧本汇编 (Visual Script Assembly)"

    def check_prerequisites(self) -> tuple[bool, str]:
        """检查前置条件"""
        # 依赖于 Stage 17 (故事流编排)
        stage17_status = self.db_manager.get_stage_status(self.video_id, 17)
        if not stage17_status or stage17_status["status"] != "completed":
            return False, "阶段17 (D2S Rewriter 故事流编排) 尚未完成"

        story_flow = self.db_manager.get_stage_output(self.video_id, 17, "story_flow")
        if not story_flow:
            return False, "数据库中未找到故事流数据"

        # 保持对其他上下文的依赖
        script_strategy = self.db_manager.get_stage_output(self.video_id, 16, "script_strategy")
        if not script_strategy:
            return False, "数据库中未找到剧本创作策略数据"

        story_outline_data = self.db_manager.get_stage_output(self.video_id, 14, "story_outline")
        if not story_outline_data:
            return False, "数据库中未找到故事大纲数据"

        return True, ""

    def execute(self, force_level: Optional[str] = None, **kwargs) -> bool:
        """执行纯视觉剧本的汇编工作流。"""
        try:
            if force_level == "full":
                self.logger.info("强制模式 'full': 清理旧的主剧本和评估报告。")
                self.db_manager.clear_master_script(self.video_id)
                self.db_manager.clear_stage_output(self.video_id, self.stage_number, "script_evaluation")

            existing_script = self.db_manager.get_master_script(self.video_id)
            if existing_script and not force_level:
                self.logger.info("已存在主剧本，跳过。")
                return True

            return self._prepare_and_run_workflow(**kwargs)

        except Exception as e:
            self.logger.error(f"执行阶段 {self.stage_number} ({self.stage_name}) 时出错: {e}", exc_info=True)
            return False

    def _prepare_and_run_workflow(self, **kwargs) -> bool:
        """
        统一的工作流，负责准备所有上下文并启动并行处理。
        """
        # 1. 加载所有必需的上下文数据
        (
            story_outline_data,
            story_flow_data,
            master_context,
            script_strategy,
            tagline,
            dossier_text,
        ) = self._load_common_context()
        if not all([story_outline_data, story_flow_data, master_context, script_strategy]):
            return False

        assert story_flow_data is not None
        scenes_in_flow = story_flow_data.get("story_flow", [])
        if not scenes_in_flow:
            self.logger.error("故事流为空，无法生成剧本。")
            return False

        assert story_outline_data is not None
        outline_map = {item["narrative_unit_number"]: item for item in story_outline_data.get("story_outline", [])}

        # 加载原始场景数据作为 Ground Truth
        original_scene_summaries = self.db_manager.get_all_scene_summaries_for_event_detection(self.video_id)
        original_scene_map = {s["scene_number"]: s for s in original_scene_summaries}

        scenes_to_process = []
        for item in scenes_in_flow:
            if item["narrative_unit_number"] in outline_map:
                outline_item = outline_map[item["narrative_unit_number"]]

                # 将原始场景数据注入到每个待处理单元中
                original_data = [
                    original_scene_map[scene_num]
                    for scene_num in outline_item.get("candidate_scene_ids", [])
                    if scene_num in original_scene_map
                ]
                outline_item["original_scene_data"] = original_data
                scenes_to_process.append(outline_item)

        if settings.DEBUG_MODE_SCENE_LIMIT > 0:
            self.logger.warning(f"--- 调试模式已激活 --- 将只处理前 {settings.DEBUG_MODE_SCENE_LIMIT} 个场景。")
            scenes_to_process = scenes_to_process[: settings.DEBUG_MODE_SCENE_LIMIT]

        # 2. 准备并行处理任务
        tasks: List[Tuple[Dict[str, Any], Optional[List[Dict[str, Any]]]]] = [
            (scene_outline, None) for scene_outline in scenes_to_process
        ]

        assert master_context is not None
        assert script_strategy is not None
        return self._parallel_process_scenes(
            tasks,
            master_context,
            script_strategy,
            tagline,
            dossier_text,
            story_flow_data,
            kwargs,
            is_refinement_only=False,  # 此参数可保留，以备未来扩展
        )

    def _format_dossier_for_prompt(self, dossier_data: Dict[str, Any]) -> str:
        """将JSON格式的角色档案转换为格式化的纯文本，用于提示词。"""
        if not dossier_data or "dossiers" not in dossier_data:
            return "无角色档案。"

        parts = []
        for dossier in dossier_data["dossiers"]:
            parts.append(
                f"character_id: {dossier.get('character_id')}\n"
                f"name: {dossier.get('name')}\n"
                f"background: {dossier.get('background')}\n"
                f"motivation: {dossier.get('motivation')}\n"
                f"conflict: {dossier.get('conflict')}\n"
                f"arc: {dossier.get('arc')}"
            )
        return "\n\n".join(parts)

    def _load_common_context(
        self,
    ) -> Tuple[
        Optional[Dict[str, Any]], Optional[Dict[str, Any]], Optional[Dict[str, Any]], Optional[Dict[str, Any]], str, str
    ]:
        """加载所有工作流共享的上下文数据。"""
        story_outline_data = self.db_manager.get_stage_output(self.video_id, 14, "story_outline")
        story_flow_data = self.db_manager.get_stage_output(self.video_id, 17, "story_flow")  # <-- 新增
        master_context = self.db_manager.get_stage_output(self.video_id, 10, "creative_brief")
        script_strategy = self.db_manager.get_stage_output(self.video_id, 16, "script_strategy")
        dossier_data = self.db_manager.get_stage_output(self.video_id, 15, "character_dossier") or {}
        dossier_text = self._format_dossier_for_prompt(dossier_data)
        tagline_data = self.db_manager.get_stage_output(self.video_id, 4, "tagline") or {}
        tagline = tagline_data.get("tagline", "无")
        return story_outline_data, story_flow_data, master_context, script_strategy, tagline, dossier_text

    def _parallel_process_scenes(
        self,
        tasks: List[Tuple[Dict[str, Any], Optional[List[Dict[str, Any]]]]],
        master_context: Dict[str, Any],
        script_strategy: Dict[str, Any],
        tagline: str,
        dossier_text: str,
        story_flow_data: Dict[str, Any],
        kwargs: Dict[str, Any],
        is_refinement_only: bool,
    ) -> bool:
        """统一的并行处理函数，用于生成或优化场景。"""
        total_scenes = len(tasks)
        completed_count, failed_count = 0, 0
        results_in_order = []

        # --- 【核心修改】记录 story_flow 定义的原始顺序 ---
        intended_order = [task[0].get("narrative_unit_number") for task in tasks]

        # Log the workflow type for debugging
        workflow_type = "优化现有剧本" if is_refinement_only else "生成新剧本"
        self.update_progress(f"开始并行处理 {total_scenes} 个场景的剧本 ({workflow_type})...")
        with concurrent.futures.ThreadPoolExecutor(max_workers=settings.MAX_CONCURRENT_REQUESTS) as executor:
            future_to_scene_num = {}
            for scene_outline, initial_draft in tasks:
                future = executor.submit(
                    self._process_single_scene_script,
                    scene_outline,
                    master_context,
                    script_strategy,
                    tagline,
                    dossier_text,
                    story_flow_data,
                    kwargs,
                    initial_draft,
                )
                future_to_scene_num[future] = scene_outline.get("narrative_unit_number")

            for future in concurrent.futures.as_completed(future_to_scene_num):
                scene_num = future_to_scene_num[future]
                try:
                    if shutdown_event.is_set():
                        for f in future_to_scene_num:
                            f.cancel()
                        break
                    scene_script = future.result()
                    if scene_script:
                        results_in_order.append({"scene_number": scene_num, "script": scene_script})
                        completed_count += 1
                    else:
                        failed_count += 1
                    progress_info = f"处理中 - 已完成 {completed_count + failed_count}/{total_scenes} (成功: {completed_count}, 失败: {failed_count})"
                    self.update_progress(progress_info)
                except Exception as exc:
                    self.logger.error(f"处理场景 {scene_num} 时产生严重异常: {exc}", exc_info=True)
                    failed_count += 1

        if failed_count > 0 or shutdown_event.is_set():
            self.logger.error("一个或多个场景未能成功生成/优化剧本，阶段失败。")
            return False

        # 3. 汇总并最终确定剧本
        final_script_beats = []
        # --- 【核心修改】按照 story_flow 的原始顺序对结果进行排序 ---
        sorted_results = sorted(
            results_in_order,
            key=lambda x: intended_order.index(x["scene_number"])
            if x["scene_number"] in intended_order
            else float("inf"),
        )
        for result in sorted_results:
            final_script_beats.extend(result["script"])

        # --- 【核心修改】对所有节拍进行全局重新编号 ---
        for i, beat in enumerate(final_script_beats):
            beat["beat_number"] = i + 1
        # --- 修改结束 ---

        # 4. 执行全局评估
        self._run_global_evaluation(final_script_beats, master_context)

        # 5. 保存最终剧本
        self.db_manager.save_master_script(self.video_id, final_script_beats)
        self.logger.info(f"✅ 完整剧本创作与修正完成，最终版本包含 {len(final_script_beats)} 个音画节拍。")
        return True

    def _process_single_scene_script(
        self,
        scene_outline: Dict[str, Any],
        master_context: Dict[str, Any],
        script_strategy: Dict[str, Any],
        tagline: str,
        dossier_text: str,
        story_flow_data: Dict[str, Any],
        kwargs: Dict[str, Any],
        initial_draft: Optional[List[Dict[str, Any]]] = None,
    ) -> Optional[List[Dict[str, Any]]]:
        """
        为单个叙事单元生成纯视觉剧本节拍。
        注意：当前已禁用基于旁白的评估-修正循环，以专注于视觉叙事。
        """
        scene_num = scene_outline.get("narrative_unit_number", "未知")

        if initial_draft:
            self.logger.info(f"发现场景 {scene_num} 的现有草稿，在纯视觉模式下将直接使用。")
            return initial_draft

        self.logger.info(f"开始为叙事单元 {scene_num} 组装纯视觉剧本节拍...")
        visual_script_beat = self._generate_script_for_scene(
            scene_outline, master_context, script_strategy, tagline, dossier_text, story_flow_data, kwargs
        )

        if not visual_script_beat:
            self.logger.error(f"叙事单元 {scene_num} 的纯视觉剧本节拍组装失败。")
            return None

        return visual_script_beat

    def _generate_script_for_scene(
        self,
        scene_outline: Dict[str, Any],
        master_context: Dict[str, Any],
        script_strategy: Dict[str, Any],
        tagline: str,
        dossier_text: str,
        story_flow_data: Dict[str, Any],
        kwargs: Dict[str, Any],
    ) -> Optional[List[Dict[str, Any]]]:
        """为单个场景生成音画节拍列表。"""

        try:
            # --- 从参数中提取所需信息 ---
            global_narration_tone = script_strategy.get("global_narration_tone", "未指定")
            genre = kwargs.get("type", "drama")
            genre_instruction = GENRE_INSTRUCTIONS.get(genre, GENRE_INSTRUCTIONS["drama"])

            # 2. 准备提示词的各个部分
            prompt = VISUAL_BEAT_ASSEMBLY_PROMPT.format(
                global_narration_tone=global_narration_tone,
                genre_instruction=genre_instruction,
                full_story_flow_json=json.dumps(story_flow_data, ensure_ascii=False, indent=2),
                characters_json=json.dumps(master_context.get("characters", []), ensure_ascii=False, indent=2),
                scene_outline_json=json.dumps(scene_outline, ensure_ascii=False, indent=2),
                language=settings.SCRIPT_LANGUAGE,
            )

            # 3. 调用AI生成剧本
            result = self.rewriter_client.call_ai_with_tool(prompt, response_model=MovieCommentaryScriptResponse)

            if result and result.script:
                # 为每个生成的节拍注入源场景编号和节拍编号
                scene_num_val = scene_outline.get("narrative_unit_number")  # <-- 修改此行
                if scene_num_val is None:
                    self.logger.error(f"场景大纲缺少必需的 'narrative_unit_number' 字段: {scene_outline}")
                    return None
                for beat in result.script:
                    beat.source_narrative_unit_number = scene_num_val
                    # beat_number 将由AI根据提示词填充为1，后续在_parallel_process_scenes中重新编号
                return [beat.model_dump() for beat in result.script]
            else:
                self.logger.warning(f"AI未能为场景 {scene_outline.get('narrative_unit_number')} 生成有效的剧本内容。")
                return None
        except Exception as e:
            self.logger.error(
                f"为场景 {scene_outline.get('narrative_unit_number')} 生成剧本时发生严重错误: {e}", exc_info=True
            )
            return None

    def _evaluate_scene_script(
        self, scene_outline: Dict[str, Any], script_beats: List[Dict[str, Any]], narration_style_preset: str
    ) -> Optional[SceneScriptEvaluationResponse]:
        """对单个场景的剧本进行评估，并传入基于音频时长的旁白策略分析。"""
        try:
            # --- 【核心修改】计算旁白预估时长 (现在从 ScriptBeat 中提取 audio_content) ---
            narration_text = " ".join(
                beat["audio_content"]
                for beat in script_beats
                if beat.get("audio_content") and beat.get("narration_type") in ["NARRATOR", "INNER_MONOLOGUE"]
            )
            estimated_narration_duration = (
                len(narration_text) / settings.NARRATION_CHAR_PER_SEC if settings.NARRATION_CHAR_PER_SEC > 0 else 0
            )

            # --- 【核心修改】获取并计算场景中所有镜头的原声对话总时长 ---
            scene_numbers_to_resolve = scene_outline.get("candidate_scene_ids", [])
            candidate_order_ids = []

            if scene_numbers_to_resolve:
                resolved_ids = set()
                for s_num in scene_numbers_to_resolve:
                    shots = self.db_manager.get_shots_by_scene_number(self.video_id, s_num)
                    for shot in shots:
                        if shot.get("shot_order_id") is not None:
                            resolved_ids.add(shot["shot_order_id"])
                candidate_order_ids = sorted(list(resolved_ids))

            candidate_shots = self.db_manager.get_shots_by_order_ids(self.video_id, candidate_order_ids)

            # 估算原声对话时长：简单地将所有有对话的镜头的时长相加
            # 这是一个合理的近似，因为在 balanced 策略下，我们期望剧本中的对话能对应到这些镜头
            total_dialogue_duration = sum(
                shot["end_time"] - shot["start_time"] for shot in candidate_shots if shot.get("dialogue")
            )

            # --- 【核心修改】构建基于音频时长的统计数据 ---
            total_audio_duration = estimated_narration_duration + total_dialogue_duration
            if total_audio_duration > 0:
                narration_ratio = estimated_narration_duration / total_audio_duration
                stats_text = (
                    f"旁白预估总时长: {estimated_narration_duration:.2f} 秒\n"
                    f"镜头原声对话总时长: {total_dialogue_duration:.2f} 秒\n"
                    f"旁白在总音轨中的时间占比: {narration_ratio:.1%}"
                )
            else:
                stats_text = "场景中无可用的音频素材时长。"

            narration_instruction = NARRATION_INTERVENTION_INSTRUCTIONS.get(narration_style_preset, "")
            # 将统计数据附加到指令中，为AI提供评估依据
            full_narration_instruction = (
                f"{narration_instruction}\n\n### 当前草稿的音频统计数据 (供你参考):\n{stats_text}"
            )

            prompt = SCENE_SCRIPT_EVALUATION_PROMPT.format(
                narration_style_instruction=full_narration_instruction,
                scene_outline_json=json.dumps(scene_outline, ensure_ascii=False, indent=2),
                generated_script_json=json.dumps(script_beats, ensure_ascii=False, indent=2),
                language=settings.SCRIPT_LANGUAGE,
            )
            return self.rewriter_client.call_ai_with_tool(prompt, response_model=SceneScriptEvaluationResponse)
        except Exception as e:
            self.logger.error(
                f"场景 {scene_outline.get('narrative_unit_number')} 的内部剧本评估失败: {e}", exc_info=True
            )
            return None

    def _refine_scene_script(
        self,
        scene_outline: Dict[str, Any],
        original_script: List[Dict[str, Any]],
        evaluation: SceneScriptEvaluationResponse,
        master_context: Dict[str, Any],
        script_strategy: Dict[str, Any],
        tagline: str,
        dossier_text: str,
        story_flow_data: Dict[str, Any],
        kwargs: Dict[str, Any],
    ) -> Optional[List[Dict[str, Any]]]:
        """根据评估报告修正单个场景的音画节拍。"""
        try:
            # --- 准备所有必需的上下文信息，与生成初稿时保持一致 ---
            global_narration_tone = script_strategy.get("global_narration_tone", "未指定")
            narration_style_preset = kwargs.get("narration_style_preset", "balanced")
            genre = kwargs.get("type", "drama")
            genre_instruction = GENRE_INSTRUCTIONS.get(genre, GENRE_INSTRUCTIONS["drama"])
            narration_intervention_instruction = NARRATION_INTERVENTION_INSTRUCTIONS.get(
                narration_style_preset, NARRATION_INTERVENTION_INSTRUCTIONS["balanced"]
            )

            prompt = SCENE_SCRIPT_SELF_CORRECTION_PROMPT.format(
                # --- 与生成提示词相同的上下文 ---
                tagline=tagline,
                global_narration_tone=global_narration_tone,
                genre_instruction=genre_instruction,
                narration_intervention_instruction=narration_intervention_instruction,
                characters_json=json.dumps(master_context.get("characters", []), ensure_ascii=False, indent=2),
                dossier_text=dossier_text,
                full_script_strategy_json=json.dumps(script_strategy, ensure_ascii=False, indent=2),
                full_story_flow_json=json.dumps(story_flow_data, ensure_ascii=False, indent=2),
                scene_outline_json=json.dumps(scene_outline, ensure_ascii=False, indent=2),
                language=settings.SCRIPT_LANGUAGE,
                # --- 新增的修正专用输入 ---
                original_script_json=json.dumps(original_script, ensure_ascii=False, indent=2),
                evaluation_report_json=json.dumps(evaluation.model_dump(), ensure_ascii=False, indent=2),
            )
            result = self.rewriter_client.call_ai_with_tool(prompt, response_model=MovieCommentaryScriptResponse)

            if result and result.script:
                scene_num = scene_outline.get("narrative_unit_number")
                if scene_num is None:
                    self.logger.error(f"场景大纲缺少必需的 'narrative_unit_number' 字段: {scene_outline}")
                    return None
                for i, beat in enumerate(result.script):
                    beat.source_narrative_unit_number = scene_num
                    beat.beat_number = i + 1  # 重新编号
                return [beat.model_dump() for beat in result.script]
            else:
                self.logger.warning(
                    f"AI未能为场景 {scene_outline.get('narrative_unit_number')} 生成有效的修正剧本内容。"
                )
                return None
        except Exception as e:
            self.logger.error(f"场景 {scene_outline.get('narrative_unit_number')} 的AI剧本修正失败: {e}", exc_info=True)
            return None

    def _run_global_evaluation(self, script_beats: List[Dict[str, Any]], master_context: Dict[str, Any]):
        """在所有场景生成完毕后，对完整剧本进行一次全局评估。"""
        self.update_progress("正在对完整剧本进行最终的全局质量评估...")
        try:
            script_text = ""
            for i, beat_container in enumerate(script_beats):
                script_text += f"\n--- 叙事单元 {beat_container['source_narrative_unit_number']} (节拍 {i + 1}) ---\n"
                for visual_beat in beat_container.get("visual_beats", []):
                    script_text += (
                        f"  - (源场景 {visual_beat['source_scene_number']}) 视觉: {visual_beat['description']}\n"
                    )
                if beat_container.get("audio_content"):
                    script_text += f"  音频 ({beat_container['narration_type']}): {beat_container['audio_content']}\n"

            prompt = SCRIPT_EVALUATION_PROMPT.format(
                project_info_json=json.dumps(master_context.get("project_info"), ensure_ascii=False, indent=2),
                characters_json=json.dumps(master_context.get("characters", []), ensure_ascii=False, indent=2),
                script_text=script_text.strip(),
                language=settings.SCRIPT_LANGUAGE,
            )
            result = self.rewriter_client.call_ai_with_tool(prompt, response_model=ScriptEvaluationResponse)
            if result:
                self.db_manager.save_stage_output(
                    video_id=self.video_id,
                    stage_number=self.stage_number,
                    stage_name=self.stage_name,
                    output_type="script_evaluation",
                    output_data=result.model_dump(),
                )
                self.logger.info("✅ 成功生成并保存了全局剧本评估报告。")
            else:
                self.logger.error("AI未能返回任何全局评估结果。")
        except Exception as e:
            self.logger.error(f"执行全局剧本评估时出错: {e}", exc_info=True)
