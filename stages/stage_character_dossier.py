import json  # 新增导入
from typing import Any, Dict, Optional

from config.prompts import CHARACTER_DOSSIER_GENERATION_PROMPT, CHARACTER_DOSSIER_REFINE_PROMPT
from config.schemas import CharacterDossierResponse, ModelName  # 新增导入
from config.settings import settings
from stages.base import BaseStage
from utils.ai_utils import secondary_client
from utils.enhanced_base_stage import EnhancedBaseStage


class Stage15CharacterDossier(BaseStage, EnhancedBaseStage):
    """阶段15：角色档案生成"""

    def __init__(self, db_manager, video_id):
        BaseStage.__init__(self, db_manager, video_id)
        EnhancedBaseStage.__init__(self, db_manager, video_id)
        self.client = secondary_client
        self.client.model = ModelName.DOUBAO_SEED_1_6.value  # 已修改

    @property
    def stage_number(self) -> int:
        return 15

    @property
    def stage_name(self) -> str:
        return "角色档案生成 (Character Dossier)"  # 更改为英文以保持一致性

    def check_prerequisites(self) -> tuple[bool, str]:
        # 依赖：6(角色自动标注) 与 10(数据基础) 均完成
        stage6_status = self.db_manager.get_stage_status(self.video_id, 6)
        if not stage6_status or stage6_status.get("status") != "completed":
            return False, "阶段6（角色自动标注）尚未完成"

        stage10_status = self.db_manager.get_stage_status(self.video_id, 10)
        if not stage10_status or stage10_status.get("status") != "completed":
            return False, "阶段10（数据基础构建）尚未完成"
        return True, ""

    def execute(self, force_level: Optional[str] = None, **kwargs) -> bool:
        """兼容性方法，调用增强的执行流程。"""
        return self.execute_with_enhancement(force_level, **kwargs)

    def _get_character_profiles_text(self) -> Optional[str]:
        """获取并格式化作为基准真相的角色设定文本，包含关系。"""
        creative_brief = self.db_manager.get_stage_output(self.video_id, 10, "creative_brief") or {}
        characters = creative_brief.get("characters", [])
        if not characters:
            self.logger.error("在阶段9的输出中缺少角色设定，无法继续。")
            return None

        prompt_parts = []
        for char in characters:
            char_info = [
                f"角色ID: {char.get('character_id', 'N/A')}",
                f"名字: {char.get('name', 'N/A')}",
                f"动机: {char.get('motivations', 'N/A')}",
                f"简介: {char.get('description', 'N/A')}",
            ]

            relationships = char.get("relationships", [])
            if relationships:
                rel_texts = []
                for rel in relationships:
                    rel_texts.append(f"  - 与 {rel.get('character_id', '?')} ({rel.get('relationship_type', '未知')})")
                char_info.append("关系:\n" + "\n".join(rel_texts))

            prompt_parts.append("\n".join(char_info))

        return "\n\n".join(prompt_parts)

    def _execute_enhanced(self, cross_stage_context: Dict, force_level: Optional[str] = None, **kwargs) -> bool:
        """带跨阶段上下文的增强执行方法。"""
        existing_dossier = self.db_manager.get_stage_output(self.video_id, self.stage_number, "character_dossier")

        if force_level == "full":
            self.logger.info("强制模式 'full': 清理旧的角色档案，将从头开始生成。")
            self.db_manager.clear_stage_output(self.video_id, self.stage_number, "character_dossier")
            return self._generate_dossier_from_scratch(cross_stage_context)

        if force_level == "soft":
            self.logger.info("--- 强制软执行模式 ---")
            if existing_dossier:
                self.logger.info("发现已存在的角色档案，将进行补全和优化。")
                return self._refine_existing_dossier(existing_dossier, cross_stage_context)
            else:
                self.logger.info("未发现已存在的角色档案，将从头开始生成。")
                return self._generate_dossier_from_scratch(cross_stage_context)

        if existing_dossier:
            self.logger.info("已存在角色档案，跳过。")
            return True

        return self._generate_dossier_from_scratch(cross_stage_context)

    def _generate_dossier_from_scratch(self, cross_stage_context: Dict) -> bool:
        """从头开始生成角色档案。"""
        self.update_progress("从头开始生成角色档案...")
        character_profiles_text = self._get_character_profiles_text()
        if not character_profiles_text:
            return False

        research_summary = self.db_manager.get_all_research_summaries(self.video_id)

        creative_brief = self.db_manager.get_stage_output(self.video_id, 10, "creative_brief") or {}
        perspective = creative_brief.get("project_info", {}).get("narration_perspective", "third_person")
        prompt = CHARACTER_DOSSIER_GENERATION_PROMPT.format(
            research_summary=research_summary,
            character_profiles_text=character_profiles_text,
            language=settings.SCRIPT_LANGUAGE,
            narration_perspective=perspective,  # 新增此行
        )

        # 添加重试指导
        retry_guidance = cross_stage_context.get("retry_guidance", {})
        if retry_guidance:
            suggestions = retry_guidance.get("improvement_suggestions", [])
            if suggestions:
                prompt += "\n\n# 重试改进指导\n请特别注意以下方面：\n" + "\n".join(f"- {s}" for s in suggestions)

        try:
            result = self.client.call_ai_with_tool(prompt, response_model=CharacterDossierResponse, temperature=0.4)
            if not result or not result.dossiers:
                self.logger.error("AI未能生成有效的角色档案。")
                return False

            self.db_manager.save_stage_output(
                self.video_id, self.stage_number, self.stage_name, "character_dossier", result.model_dump()
            )
            self.logger.info("✅ 角色档案生成成功。")
            return True
        except Exception as e:
            self.logger.error(f"执行阶段 {self.stage_number} ({self.stage_name}) 时出错: {e}", exc_info=True)
            return False

    def _refine_existing_dossier(self, existing_dossier: Dict[str, Any], cross_stage_context: Dict) -> bool:
        """在现有档案的基础上进行补全和优化。"""
        self.update_progress("正在优化和补全现有角色档案...")
        character_profiles_text = self._get_character_profiles_text()
        if not character_profiles_text:
            return False

        if not existing_dossier or "dossiers" not in existing_dossier:
            self.logger.warning("现有档案内容为空或格式不正确，将回退到从头开始生成。")
            return self._generate_dossier_from_scratch(cross_stage_context)

        research_summary = self.db_manager.get_all_research_summaries(self.video_id)

        creative_brief = self.db_manager.get_stage_output(self.video_id, 10, "creative_brief") or {}
        perspective = creative_brief.get("project_info", {}).get("narration_perspective", "third_person")
        prompt = CHARACTER_DOSSIER_REFINE_PROMPT.format(
            research_summary=research_summary,
            character_profiles_text=character_profiles_text,
            existing_dossier_json=json.dumps(existing_dossier, ensure_ascii=False, indent=2),
            language=settings.SCRIPT_LANGUAGE,
            narration_perspective=perspective,  # 新增此行
        )

        # 添加重试指导
        retry_guidance = cross_stage_context.get("retry_guidance", {})
        if retry_guidance:
            suggestions = retry_guidance.get("improvement_suggestions", [])
            if suggestions:
                prompt += "\n\n# 重试改进指导\n请特别注意以下方面：\n" + "\n".join(f"- {s}" for s in suggestions)

        try:
            result = self.client.call_ai_with_tool(prompt, response_model=CharacterDossierResponse, temperature=0.5)
            if not result or not result.dossiers:
                self.logger.error("AI未能生成有效的精炼后角色档案。")
                return False

            self.db_manager.save_stage_output(
                self.video_id, self.stage_number, self.stage_name, "character_dossier", result.model_dump()
            )
            self.logger.info("✅ 角色档案优化补全成功。")
            return True
        except Exception as e:
            self.logger.error(f"执行阶段 {self.stage_number} ({self.stage_name}) 时出错: {e}", exc_info=True)
            return False

    def get_stage_output(self) -> Dict:
        """获取阶段输出数据，用于质量评估。"""
        return self.db_manager.get_stage_output(self.video_id, self.stage_number, "character_dossier") or {}
