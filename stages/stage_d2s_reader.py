"""
阶段11：D2S 读者模块 (Reader)
第一步：事件识别
"""

import json
from typing import Dict, List, Optional

from config.prompts import EVENT_IDENTIFICATION_PROMPT
from config.schemas import EventIdentificationResponse, ModelName
from config.settings import settings
from stages.base import BaseStage
from utils.ai_utils import primary_client
from utils.enhanced_base_stage import EnhancedBaseStage


class Stage11Reader(BaseStage, EnhancedBaseStage):
    """阶段11：D2S 读者模块 - 事件识别"""

    def __init__(self, db_manager, video_id):
        BaseStage.__init__(self, db_manager, video_id)
        EnhancedBaseStage.__init__(self, db_manager, video_id)
        self.reader_client = primary_client
        self.reader_client.model = ModelName.DOUBAO_SEED_1_6.value  # 已修改

    @property
    def stage_number(self) -> int:
        return 11

    @property
    def stage_name(self) -> str:
        return "D2S Reader (事件识别)"

    def check_prerequisites(self) -> tuple[bool, str]:
        """检查前置条件"""
        # 依赖于阶段10（数据基础构建）完成
        stage10_status = self.db_manager.get_stage_status(self.video_id, 10)
        if not stage10_status or stage10_status["status"] != "completed":
            return False, "阶段10 (数据基础构建) 尚未完成"

        return True, ""

    def execute(self, force_level: Optional[str] = None, **kwargs) -> bool:
        """执行阶段处理，使用增强功能"""
        return self.execute_with_enhancement(force_level, **kwargs)

    def _execute_enhanced(self, cross_stage_context: Dict, force_level: Optional[str] = None, **kwargs) -> bool:
        """带跨阶段上下文的增强执行方法。"""
        if force_level:
            self.logger.info(f"强制模式 ({force_level}): 清理旧的叙事事件数据。")
            self.db_manager.clear_stage_output(self.video_id, self.stage_number, "narrative_events")

        existing_output = self.db_manager.get_stage_output(self.video_id, self.stage_number, "narrative_events")
        if existing_output and not force_level:
            self.logger.info("从数据库加载已缓存的叙事事件数据。")
            return True

        self.update_progress("从数据库加载场景摘要...")
        scene_summaries = self.db_manager.get_all_scene_summaries_for_event_detection(self.video_id)
        if not scene_summaries:
            self.logger.error("数据库中没有找到任何场景摘要，无法进行事件识别。")
            return False

        self.update_progress("过滤不重要角色以优化AI输入...")
        unimportant_keywords = ["未知", "已跳过"]
        for scene in scene_summaries:
            if "characters_present" in scene:
                scene["characters_present"] = [
                    name
                    for name in scene["characters_present"]
                    if not any(keyword in name for keyword in unimportant_keywords)
                ]

        self.update_progress("AI正在基于场景摘要识别叙事事件...")
        try:
            enhanced_prompt = self._build_enhanced_prompt(scene_summaries, cross_stage_context)
            result = self.reader_client.call_ai_with_tool(enhanced_prompt, response_model=EventIdentificationResponse)

            if not result or not result.narrative_events:
                self.logger.error("AI未能识别出任何叙事事件。")
                return False

            for i, event in enumerate(result.narrative_events):
                event.event_id = f"event_{i + 1:03d}"

            original_event_count = len(result.narrative_events)
            importance_threshold = 0.4

            filtered_events = [
                event for event in result.narrative_events if event.importance_score >= importance_threshold
            ]

            if len(filtered_events) < original_event_count:
                self.logger.info(
                    f"根据重要性评分 (>{importance_threshold}) 过滤了 {original_event_count - len(filtered_events)} 个次要事件。"
                )

            result.narrative_events = filtered_events

            self.db_manager.save_stage_output(
                video_id=self.video_id,
                stage_number=self.stage_number,
                stage_name=self.stage_name,
                output_type="narrative_events",
                output_data=result.model_dump(),
            )

            self.logger.info(f"✅ 成功识别并保存 {len(result.narrative_events)} 个叙事事件到数据库。")
            return True

        except Exception as e:
            self.logger.error(f"执行阶段 {self.stage_number} ({self.stage_name}) 时出错: {e}", exc_info=True)
            return False

    def _build_enhanced_prompt(self, scene_summaries: List[Dict], cross_stage_context: Dict) -> str:
        """构建包含跨阶段上下文和重试指导的增强提示词。"""
        base_prompt = EVENT_IDENTIFICATION_PROMPT.format(
            scene_data_json=json.dumps(scene_summaries, ensure_ascii=False, indent=2),
            language=settings.SCRIPT_LANGUAGE,
        )

        # 添加角色上下文信息
        character_context = cross_stage_context.get("character_context", {})
        if character_context:
            character_info = json.dumps(character_context.get("character_profiles", {}), ensure_ascii=False, indent=2)
            base_prompt += f"\n\n# 角色上下文信息\n以下是已识别的主要角色档案，请在分析事件时参考：\n{character_info}"

        # 添加重试指导（如果存在）
        retry_guidance = cross_stage_context.get("retry_guidance", {})
        if retry_guidance:
            suggestions = retry_guidance.get("improvement_suggestions", [])
            if suggestions:
                base_prompt += "\n\n# 重试改进指导\n请特别注意以下方面：\n" + "\n".join(f"- {s}" for s in suggestions)

        return base_prompt

    def get_stage_output(self) -> Dict:
        """获取阶段输出数据，用于质量评估。"""
        return self.db_manager.get_stage_output(self.video_id, self.stage_number, "narrative_events") or {}
